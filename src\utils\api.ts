/**
 * Fetch dengan session handling
 * Akan otomatis redirect ke halaman login jika session expired
 */
export const fetchWithSession = async (url: string, options: RequestInit = {}) => {
  try {
    const response = await fetch(url, options);

    // Jika response status 401 (Unauthorized), redirect ke login
    if (response.status === 401) {
      // Clear session storage
      sessionStorage.removeItem('isLoggedIn');
      sessionStorage.removeItem('currentUser');

      // Redirect ke login
      window.location.href = '/login';
      return null;
    }

    return response;
  } catch (error) {
    throw error;
  }
};

// Fungsi untuk mendapatkan data user saat ini
export const getCurrentUser = () => {
  const userData = sessionStorage.getItem('currentUser');
  if (!userData) return null;

  try {
    return JSON.parse(userData);
  } catch (error) {
    return null;
  }
};

// Fungsi untuk memeriksa apakah user sudah login
export const isUserLoggedIn = () => {
  return sessionStorage.getItem('isLoggedIn') === 'true';
};

// Fungsi untuk logout
export const logout = () => {
  sessionStorage.removeItem('isLoggedIn');
  sessionStorage.removeItem('currentUser');
  window.location.href = '/login';
};

// Fungsi untuk membuat URL dengan parameter yang benar untuk kasir
export const buildApiUrl = (baseUrl: string, additionalParams: Record<string, string | number> = {}) => {
  const user = getCurrentUser();
  if (!user) return baseUrl;

  const url = new URL(baseUrl);

  // Selalu tambahkan user_id
  url.searchParams.append('user_id', user.id.toString());

  // Jika user adalah kasir, tambahkan cashier_id
  if (user.role === 'cashier' && user.cashier_id) {
    url.searchParams.append('cashier_id', user.cashier_id.toString());
  }

  // Tambahkan parameter tambahan
  Object.entries(additionalParams).forEach(([key, value]) => {
    url.searchParams.append(key, value.toString());
  });

  return url.toString();
};

