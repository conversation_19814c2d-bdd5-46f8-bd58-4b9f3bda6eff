const express = require('express');
const router = express.Router();
const pool = require('../config/database');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const StoreConfig = require('../models/StoreConfig');

// Konfigurasi multer untuk upload logo
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads/logo');

    // Buat direktori jika belum ada
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'logo-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 2 * 1024 * 1024 }, // 2MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (extname && mimetype) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'));
    }
  }
});

// Get store configuration
router.get('/', async (req, res) => {
  try {
    const { user_id } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const [rows] = await pool.query(
      `SELECT * FROM ${StoreConfig.$table} WHERE user_id = ? LIMIT 1`,
      [user_id]
    );

    if (rows.length === 0) {
      return res.status(404).json({ error: 'Store configuration not found' });
    }

    // Tambahkan URL lengkap untuk logo jika ada
    if (rows[0].logo) {
      rows[0].logo_url = `${req.protocol}://${req.get('host')}/uploads/logo/${rows[0].logo}`;
    }

    res.json(rows[0]);
  } catch (error) {
    console.error('Error fetching store config:', error);
    res.status(500).json({ error: 'Failed to fetch store configuration' });
  }
});

// Update store configuration
router.put('/', upload.single('logo'), async (req, res) => {
  try {
    const {
      store_name,
      store_address,
      store_phone,
      store_email,
      tax_percentage,
      currency,
      receipt_footer,
      user_id
    } = req.body;

    // Validasi input
    if (!store_name || !user_id) {
      return res.status(400).json({ error: 'Store name and user ID are required' });
    }

    // Validate that store name is not just spaces
    if (!store_name.trim()) {
      return res.status(400).json({ error: 'Nama toko tidak boleh hanya berisi spasi' });
    }

    // Cek apakah konfigurasi toko sudah ada untuk user ini
    const [existingConfig] = await pool.query(
      `SELECT * FROM ${StoreConfig.$table} WHERE user_id = ?`,
      [user_id]
    );

    // Cek apakah ada file logo yang diupload
    let logoFileName = null;
    if (req.file) {
      logoFileName = req.file.filename;

      // Hapus logo lama jika ada
      if (existingConfig.length > 0 && existingConfig[0].logo) {
        const oldLogoPath = path.join(__dirname, '../uploads/logo', existingConfig[0].logo);
        if (fs.existsSync(oldLogoPath)) {
          fs.unlinkSync(oldLogoPath);
        }
      }
    }

    let query, params;

    if (existingConfig.length > 0) {
      // Update konfigurasi yang sudah ada
      query = `
        UPDATE ${StoreConfig.$table} SET
        store_name = ?,
        store_address = ?,
        store_phone = ?,
        store_email = ?,
        tax_percentage = ?,
        currency = ?,
        receipt_footer = ?
      `;

      params = [
        store_name,
        store_address || null,
        store_phone || null,
        store_email || null,
        tax_percentage || 0,
        currency || 'IDR',
        receipt_footer || null
      ];

      // Tambahkan logo ke query jika ada
      if (logoFileName) {
        query += `, logo = ?`;
        params.push(logoFileName);
      }

      query += ` WHERE user_id = ?`;
      params.push(user_id);
    } else {
      // Buat konfigurasi baru jika belum ada
      query = `
        INSERT INTO ${StoreConfig.$table} (
          store_name, 
          store_address, 
          store_phone, 
          store_email, 
          tax_percentage, 
          currency, 
          receipt_footer, 
          logo, 
          user_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      params = [
        store_name,
        store_address || null,
        store_phone || null,
        store_email || null,
        tax_percentage || 0,
        currency || 'IDR',
        receipt_footer || null,
        logoFileName,
        user_id
      ];
    }

    await pool.query(query, params);

    // Ambil data yang sudah diupdate
    const [updatedConfig] = await pool.query(
      `SELECT * FROM ${StoreConfig.$table} WHERE user_id = ?`,
      [user_id]
    );

    // Tambahkan URL lengkap untuk logo jika ada
    if (updatedConfig[0].logo) {
      updatedConfig[0].logo_url = `${req.protocol}://${req.get('host')}/uploads/logo/${updatedConfig[0].logo}`;
    }

    res.json(updatedConfig[0]);
  } catch (error) {
    console.error('Error updating store config:', error);
    res.status(500).json({ error: 'Failed to update store configuration' });
  }
});

module.exports = router;
