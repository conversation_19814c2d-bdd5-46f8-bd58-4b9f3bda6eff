const express = require('express');
const mysql = require('mysql2/promise');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
require('dotenv').config();

// Set timezone untuk Node.js
process.env.TZ = 'Asia/Jakarta';
console.log(`Server timezone set to: ${process.env.TZ}`);

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
// Serve icon files
app.use('/uploads/icons', express.static(path.join(__dirname, 'uploads/icons')));

// Database connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'satulisa_db',
  password: process.env.DB_PASSWORD || 'owr216he890',
  database: process.env.DB_NAME || 'satulisa_db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  timezone: '+07:00' // Set timezone ke Asia/Jakarta (GMT+7)
});

// Test database connection
app.get('/api/test', async (req, res) => {
  try {
    const connection = await pool.getConnection();
    connection.release();
    res.json({ message: 'Database connection successful' });
  } catch (error) {
    console.error('Database connection error:', error);
    res.status(500).json({ error: 'Database connection failed' });
  }
});

// API routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/branches', require('./routes/branches'));
app.use('/api/products', require('./routes/products'));
app.use('/api/categories', require('./routes/categories'));
app.use('/api/transactions', require('./routes/transactions'));
app.use('/api/users', require('./routes/users'));
app.use('/api/members', require('./routes/members'));
app.use('/api/uploads', require('./routes/uploads'));
app.use('/api/payment-methods', require('./routes/paymentMethods'));
app.use('/api/payment-gateway', require('./routes/paymentGateway'));
app.use('/api/store-config', require('./routes/storeConfig'));
app.use('/api/payment-gateways', require('./routes/paymentGateways'));
app.use('/api/members', require('./routes/members'));
app.use('/api/cashiers', require('./routes/cashiers'));
app.use('/api/dashboard', require('./routes/dashboard'));

// Tambahkan log untuk memastikan route terdaftar
console.log('Registered routes:', app._router.stack
  .filter(r => r.route)
  .map(r => r.route.path));

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});




