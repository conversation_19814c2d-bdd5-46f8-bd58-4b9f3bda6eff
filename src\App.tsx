import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { CartProvider } from './context/CartContext';
import Header from './components/Header';
import { Clock, Search, Calendar } from 'lucide-react';
import CategoryMenu from './components/CategoryMenu';
import ProductGrid from './components/ProductGrid';
import OrderPanel from './components/OrderPanel';
import Login from './pages/Login';
import ProductManagement from './pages/ProductManagement';
import ProductForm from './pages/ProductForm';
import CategoryManagement from './pages/CategoryManagement';
import CategoryForm from './pages/CategoryForm';
import BranchManagement from './pages/BranchManagement';
import BranchForm from './pages/BranchForm';
import ReportsPage from './pages/ReportsPage';
import TransactionHistory from './pages/TransactionHistory';
// import UserManagement from './pages/UserManagement';
import UserForm from './pages/UserForm';
import config from './config';
import ChangePassword from './pages/ChangePassword';
import PaymentMethodManagement from './pages/PaymentMethodManagement';
import StoreConfigPage from './pages/StoreConfigPage';
import PaymentGatewayConfig from './pages/PaymentGatewayConfig';
import MemberManagement from './pages/MemberManagement';
import MemberForm from './pages/MemberForm';
import LandingPage from './pages/LandingPage';
import CashierManagement from './pages/CashierManagement';
import CashierForm from './pages/CashierForm';
import AccessDenied from './components/AccessDenied';
import DashboardSummary from './components/DashboardSummary';

// Protected Route Component
const ProtectedRoute = ({
  children,
  isAllowed,
  userRole,
  requiredRole,
  redirectToLogin = false
}: {
  children: JSX.Element,
  isAllowed: boolean,
  userRole?: string,
  requiredRole?: string,
  redirectToLogin?: boolean
}) => {
  if (!isAllowed) {
    if (redirectToLogin) {
      return <Navigate to="/login" replace />;
    }
    return <AccessDenied userRole={userRole} requiredRole={requiredRole} />;
  }
  return children;
};

function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [currentUser, setCurrentUser] = useState<{
    id: string;
    email: string;
    name: string;
    role: 'admin' | 'branch' | 'cashier';
  } | null>(null);

  // Check if user is already logged in
  useEffect(() => {
    const loggedInStatus = sessionStorage.getItem('isLoggedIn');
    const userData = sessionStorage.getItem('currentUser');

    if (loggedInStatus === 'true' && userData) {
      setIsLoggedIn(true);
      setCurrentUser(JSON.parse(userData));
    }
  }, []);

  // Tambahkan fungsi untuk memeriksa session expired
  const checkSessionExpired = () => {
    const loggedInStatus = sessionStorage.getItem('isLoggedIn');
    if (isLoggedIn && !loggedInStatus) {
      // Session telah expired atau dihapus
      setIsLoggedIn(false);
      setCurrentUser(null);
    }
  };

  // Tambahkan event listener untuk storage changes
  useEffect(() => {
    window.addEventListener('storage', checkSessionExpired);

    // Periksa session setiap kali tab mendapatkan fokus
    window.addEventListener('focus', checkSessionExpired);

    return () => {
      window.removeEventListener('storage', checkSessionExpired);
      window.removeEventListener('focus', checkSessionExpired);
    };
  }, [isLoggedIn]);

  const handleLogin = async (email: string, password: string) => {
    try {
      // Panggil API untuk login
      const response = await fetch(`${config.apiUrl}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        return false;
      }

      const userData = await response.json();

      sessionStorage.setItem('isLoggedIn', 'true');
      sessionStorage.setItem('currentUser', JSON.stringify(userData));

      setIsLoggedIn(true);
      setCurrentUser(userData);
      return true;
    } catch (error) {
      return false;
    }
  };

  const handleLogout = () => {
    sessionStorage.removeItem('isLoggedIn');
    sessionStorage.removeItem('currentUser');
    setIsLoggedIn(false);
    setCurrentUser(null);
  };

  const isAdmin = currentUser?.role === 'admin';
  const isCashier = currentUser?.role === 'cashier';
  const isBranch = currentUser?.role === 'branch';

  return (
    <CartProvider>
      <Router>
        <div className="min-h-screen bg-neutral-50">
          {isLoggedIn && currentUser?.role && (
            <Header onLogout={handleLogout} userName={currentUser?.name || 'User'} userRole={currentUser?.role} />
          )}

          <Routes>
            {/* Landing Page - Public, redirect based on role */}
            <Route path="/" element={
              isLoggedIn && currentUser?.role ? (
                isCashier ? <Navigate to="/dashboard" replace /> :
                  <Navigate to="/dashboard-summary" replace />
              ) : <LandingPage />
            } />

            {/* Login Route */}
            <Route path="/login" element={
              isLoggedIn && currentUser?.role ? (
                isCashier ? <Navigate to="/dashboard" replace /> :
                  <Navigate to="/dashboard-summary" replace />
              ) : <Login onLogin={handleLogin} />
            } />

            {/* Dashboard Routes */}
            {/* POS Screen - Only accessible by cashiers */}
            <Route path="/dashboard" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && isCashier}
                userRole={currentUser?.role}
                requiredRole="kasir"
                redirectToLogin={!isLoggedIn}
              >
                <div className="container mx-auto p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2 text-neutral-500">
                      <Calendar size={18} />
                      <span className="text-sm">
                        {new Date().toLocaleDateString('id-ID', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          timeZone: 'Asia/Jakarta'
                        })}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-neutral-500">
                      <Clock size={18} />
                      <span className="text-sm">
                        {new Date().toLocaleTimeString('id-ID', {
                          hour: '2-digit',
                          minute: '2-digit',
                          timeZone: 'Asia/Jakarta'
                        })}
                      </span>
                    </div>
                  </div>

                  {/* Main Content */}
                  <div className="flex flex-col md:flex-row h-full gap-4">
                    {/* Left Side - Categories and Products */}
                    <div className="flex-1 flex flex-col h-full">
                      {/* Search and Categories */}
                      <div className="mb-4">
                        <div className="relative mb-4">
                          <input
                            type="text"
                            placeholder="Cari menu..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="w-full pl-10 pr-4 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                            autoComplete="off"
                          />
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400" size={18} />
                        </div>

                        <CategoryMenu
                          selectedCategory={selectedCategory}
                          onSelectCategory={setSelectedCategory}
                        />
                      </div>

                      {/* Product Grid */}
                      <div className="flex-1 overflow-y-auto">
                        <ProductGrid
                          category={selectedCategory}
                          searchQuery={searchQuery}
                        />
                      </div>
                    </div>

                    {/* Order Panel - Full width pada mobile, fixed width pada desktop */}
                    <div className="w-full md:w-[380px] h-[500px] md:h-auto bg-white rounded-xl shadow-sm mt-4 md:mt-0">
                      <OrderPanel />
                    </div>
                  </div>
                </div>
              </ProtectedRoute>
            } />

            {/* Dashboard Summary - Only accessible by admin and branch */}
            <Route path="/dashboard-summary" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && (isAdmin || isBranch)}
                userRole={currentUser?.role}
                requiredRole="admin atau branch"
                redirectToLogin={!isLoggedIn}
              >
                <div className="container mx-auto p-4">
                  <DashboardSummary
                    userRole={currentUser?.role as 'admin' | 'branch'}
                    userId={parseInt(currentUser?.id || '0')}
                    branchId={currentUser?.role === 'branch' ? parseInt(currentUser?.id || '0') : undefined}
                  />
                </div>
              </ProtectedRoute>
            } />

            {/* Product Management Routes - Accessible by admin and branch users */}
            <Route path="/products" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && (isAdmin || isBranch)}
                userRole={currentUser?.role}
                requiredRole="admin atau branch"
                redirectToLogin={!isLoggedIn}
              >
                <ProductManagement />
              </ProtectedRoute>
            } />
            <Route path="/product/new" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && (isAdmin || isBranch)}
                userRole={currentUser?.role}
                requiredRole="admin atau branch"
                redirectToLogin={!isLoggedIn}
              >
                <ProductForm />
              </ProtectedRoute>
            } />
            <Route path="/product/edit/:id" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && (isAdmin || isBranch)}
                userRole={currentUser?.role}
                requiredRole="admin atau branch"
                redirectToLogin={!isLoggedIn}
              >
                <ProductForm />
              </ProtectedRoute>
            } />

            {/* Category Management Routes - Accessible by admin and branch users */}
            <Route path="/categories" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && (isAdmin || isBranch)}
                userRole={currentUser?.role}
                requiredRole="admin atau branch"
                redirectToLogin={!isLoggedIn}
              >
                <CategoryManagement />
              </ProtectedRoute>
            } />
            <Route path="/category/new" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && (isAdmin || isBranch)}
                userRole={currentUser?.role}
                requiredRole="admin atau branch"
                redirectToLogin={!isLoggedIn}
              >
                <CategoryForm />
              </ProtectedRoute>
            } />
            <Route path="/category/edit/:id" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && (isAdmin || isBranch)}
                userRole={currentUser?.role}
                requiredRole="admin atau branch"
                redirectToLogin={!isLoggedIn}
              >
                <CategoryForm />
              </ProtectedRoute>
            } />

            {/* Protected Admin Only Routes */}
            <Route path="/branches" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && isAdmin}
                userRole={currentUser?.role}
                requiredRole="admin"
                redirectToLogin={!isLoggedIn}
              >
                <BranchManagement />
              </ProtectedRoute>
            } />
            <Route path="/branch/new" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && isAdmin}
                userRole={currentUser?.role}
                requiredRole="admin"
                redirectToLogin={!isLoggedIn}
              >
                <BranchForm />
              </ProtectedRoute>
            } />
            <Route path="/branch/edit/:id" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && isAdmin}
                userRole={currentUser?.role}
                requiredRole="admin"
                redirectToLogin={!isLoggedIn}
              >
                <BranchForm />
              </ProtectedRoute>
            } />
            <Route path="/reports" element={
              <ProtectedRoute
                isAllowed={isLoggedIn}
                userRole={currentUser?.role}
                requiredRole="admin atau branch"
                redirectToLogin={!isLoggedIn}
              >
                <ReportsPage />
              </ProtectedRoute>
            } />
            <Route path="/payment-methods" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && isAdmin}
                userRole={currentUser?.role}
                requiredRole="admin"
                redirectToLogin={!isLoggedIn}
              >
                <PaymentMethodManagement />
              </ProtectedRoute>
            } />

            {/* Reports Route - Accessible by admin and branch users */}
            <Route path="/reports" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && (isAdmin || isBranch)}
                userRole={currentUser?.role}
                requiredRole="admin atau branch"
                redirectToLogin={!isLoggedIn}
              >
                <ReportsPage />
              </ProtectedRoute>
            } />

            {/* Transaction Routes - Accessible by all logged in users */}
            <Route path="/transactions" element={
              <ProtectedRoute
                isAllowed={isLoggedIn}
                userRole={currentUser?.role}
                requiredRole="semua pengguna"
                redirectToLogin={!isLoggedIn}
              >
                <TransactionHistory />
              </ProtectedRoute>
            } />

            {/* User Management Routes - Accessible by admin */}
            {/* <Route path="/users" element={
              <ProtectedRoute isAllowed={isLoggedIn && isAdmin}>
                <UserManagement />
              </ProtectedRoute>
            } /> */}
            <Route path="/user/new" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && isAdmin}
                userRole={currentUser?.role}
                requiredRole="admin"
                redirectToLogin={!isLoggedIn}
              >
                <UserForm />
              </ProtectedRoute>
            } />
            <Route path="/user/edit/:id" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && isAdmin}
                userRole={currentUser?.role}
                requiredRole="admin"
                redirectToLogin={!isLoggedIn}
              >
                <UserForm />
              </ProtectedRoute>
            } />

            {/* Change Password Route - Accessible by all logged in users */}
            <Route path="/change-password" element={
              <ProtectedRoute
                isAllowed={isLoggedIn}
                userRole={currentUser?.role}
                requiredRole="semua pengguna"
                redirectToLogin={!isLoggedIn}
              >
                <ChangePassword />
              </ProtectedRoute>
            } />

            {/* Payment Method Management Route - Accessible by admin */}
            <Route path="/payment-methods" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && isAdmin}
                userRole={currentUser?.role}
                requiredRole="admin"
                redirectToLogin={!isLoggedIn}
              >
                <PaymentMethodManagement />
              </ProtectedRoute>
            } />

            {/* Store Config Route - Accessible by admin */}
            <Route path="/store-config" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && isAdmin}
                userRole={currentUser?.role}
                requiredRole="admin"
                redirectToLogin={!isLoggedIn}
              >
                <StoreConfigPage />
              </ProtectedRoute>
            } />

            {/* Payment Gateway Config Route - Accessible by admin */}
            <Route path="/payment-gateway-config" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && isAdmin}
                userRole={currentUser?.role}
                requiredRole="admin"
                redirectToLogin={!isLoggedIn}
              >
                <PaymentGatewayConfig />
              </ProtectedRoute>
            } />

            {/* Member Management Routes - Accessible by admin */}
            <Route
              path="/members"
              element={
                <ProtectedRoute
                  isAllowed={isLoggedIn && isAdmin}
                  userRole={currentUser?.role}
                  requiredRole="admin"
                  redirectToLogin={!isLoggedIn}
                >
                  <MemberManagement />
                </ProtectedRoute>
              }
            />
            <Route
              path="/member/new"
              element={
                <ProtectedRoute
                  isAllowed={isLoggedIn}
                  userRole={currentUser?.role}
                  requiredRole="admin"
                  redirectToLogin={!isLoggedIn}
                >
                  <MemberForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/member/edit/:id"
              element={
                <ProtectedRoute
                  isAllowed={isLoggedIn}
                  userRole={currentUser?.role}
                  requiredRole="admin"
                  redirectToLogin={!isLoggedIn}
                >
                  <MemberForm />
                </ProtectedRoute>
              }
            />

            {/* Cashier Management Routes - Accessible by admin and branch */}
            <Route path="/cashier-management" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && (isAdmin || isBranch)}
                userRole={currentUser?.role}
                requiredRole="admin atau branch"
                redirectToLogin={!isLoggedIn}
              >
                <CashierManagement />
              </ProtectedRoute>
            } />
            <Route path="/cashier/new" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && (isAdmin || isBranch)}
                userRole={currentUser?.role}
                requiredRole="admin atau branch"
                redirectToLogin={!isLoggedIn}
              >
                <CashierForm />
              </ProtectedRoute>
            } />
            <Route path="/cashier/edit/:id" element={
              <ProtectedRoute
                isAllowed={isLoggedIn && (isAdmin || isBranch)}
                userRole={currentUser?.role}
                requiredRole="admin atau branch"
                redirectToLogin={!isLoggedIn}
              >
                <CashierForm />
              </ProtectedRoute>
            } />

            {/* Redirect any unknown routes to home */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </div>
      </Router>
    </CartProvider>
  );
}

export default App;











