const express = require('express');
const router = express.Router();
const pool = require('../config/database');

// Get all categories
router.get('/', async (req, res) => {
  try {
    let { user_id, cashier_id } = req.query;
    let { branch_id } = req.query;

    // Store original user_id for admin cashier logic
    const original_user_id = user_id;

    // If the request is from a cashier, determine the branch_id and admin_id
    if (cashier_id) {
      const [cashierResult] = await pool.query(
        'SELECT c.branch_id, b.user_id as admin_id FROM cashiers c LEFT JOIN branches b ON c.branch_id = b.id WHERE c.id = ? AND c.is_active = 1',
        [cashier_id]
      );

      if (cashierResult.length === 0) {
        return res.status(404).json({ error: 'Cashier not found' });
      }

      branch_id = cashierResult[0].branch_id;
      // Untuk kasir cabang, gunakan branch_id sebagai user_id agar melihat kategori cabang
      if (cashierResult[0].branch_id) {
        user_id = cashierResult[0].branch_id;
      } else if (cashierResult[0].admin_id) {
        // Untuk kasir admin langsung, gunakan admin_id
        user_id = cashierResult[0].admin_id;
      }
    }

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Check if user is a branch user
    const [branchUser] = await pool.query(
      'SELECT * FROM branches WHERE id = ? AND is_active = 1',
      [user_id]
    );

    let query, params;

    if (branchUser.length > 0) {
      // User is a branch user - only show categories they created themselves
      query = 'SELECT * FROM categories WHERE user_id = ?';
      params = [user_id];
    } else if (branch_id) {
      // Admin user with specific branch filter - show admin categories
      const [branchResult] = await pool.query(
        'SELECT user_id FROM branches WHERE id = ? AND user_id = ?',
        [branch_id, user_id]
      );

      if (branchResult.length === 0) {
        return res.status(403).json({ error: 'Access denied to this branch' });
      }

      query = 'SELECT * FROM categories WHERE user_id = ?';
      params = [user_id];
    } else {
      // Admin user without branch filter - show all their categories
      query = 'SELECT * FROM categories WHERE user_id = ?';
      params = [user_id];
    }

    query += ' ORDER BY label ASC';

    // Ambil kategori dari database
    const [rows] = await pool.query(query, params);

    // Pastikan kategori 'all' selalu ada
    const allCategoryExists = rows.some(cat => cat.value === 'all');

    if (!allCategoryExists) {
      // Tambahkan kategori 'all' jika belum ada, dengan user_id yang sesuai
      rows.unshift({
        id: 0,
        value: 'all',
        label: 'All Menu',
        icon: '🍽️',
        count: 0,
        user_id: parseInt(user_id)
      });
    }

    // Hitung jumlah produk untuk setiap kategori
    let productQuery = 'SELECT category, COUNT(*) as count FROM products WHERE is_active = 1';
    const productParams = [];

    // Gunakan logika yang sama dengan products API untuk konsistensi
    if (cashier_id) {
      // Request from cashier
      if (branch_id) {
        // Cashier milik cabang - hanya hitung produk dari cabang tersebut
        productQuery += ' AND branch_id = ?';
        productParams.push(branch_id);
      } else {
        // Cashier tanpa cabang (direct admin cashier) - hitung admin products only
        productQuery += ' AND user_id = ? AND branch_id IS NULL';
        productParams.push(original_user_id);
      }
    } else if (branchUser.length > 0) {
      // User is a branch user - count only products they created themselves
      productQuery += ' AND branch_id = ?';
      productParams.push(user_id);
    } else if (branch_id) {
      // Admin user with specific branch filter - count products from that branch and admin products
      productQuery += ' AND (branch_id = ? OR (user_id = ? AND branch_id IS NULL))';
      productParams.push(branch_id, user_id);
    } else {
      // Admin user without branch filter - count all their products and branch products
      productQuery += ' AND (user_id = ? OR branch_id IN (SELECT id FROM branches WHERE user_id = ?))';
      productParams.push(user_id, user_id);
    }

    productQuery += ' GROUP BY category';

    const [products] = await pool.query(productQuery, productParams);

    // Buat map untuk menyimpan jumlah produk per kategori
    const categoryCounts = {};
    products.forEach(product => {
      categoryCounts[product.category] = product.count;
    });

    // Hitung total produk untuk kategori 'all' - gunakan logika yang sama dengan products API
    let totalProductQuery = 'SELECT COUNT(*) as count FROM products WHERE is_active = 1';
    const totalProductParams = [];

    // Gunakan logika yang sama dengan products API untuk konsistensi
    if (cashier_id) {
      // Request from cashier
      if (branch_id) {
        // Cashier milik cabang - hanya hitung produk dari cabang tersebut
        totalProductQuery += ' AND branch_id = ?';
        totalProductParams.push(branch_id);
      } else {
        // Cashier tanpa cabang (direct admin cashier) - hitung admin products only
        totalProductQuery += ' AND user_id = ? AND branch_id IS NULL';
        totalProductParams.push(original_user_id);
      }
    } else if (branchUser.length > 0) {
      // User is a branch user - count only products they created themselves
      totalProductQuery += ' AND branch_id = ?';
      totalProductParams.push(user_id);
    } else if (branch_id) {
      // Admin user with specific branch filter - count products from that branch and admin products
      totalProductQuery += ' AND (branch_id = ? OR (user_id = ? AND branch_id IS NULL))';
      totalProductParams.push(branch_id, user_id);
    } else {
      // Admin user without branch filter - count all their products and branch products
      totalProductQuery += ' AND (user_id = ? OR branch_id IN (SELECT id FROM branches WHERE user_id = ?))';
      totalProductParams.push(user_id, user_id);
    }

    const [totalProducts] = await pool.query(totalProductQuery, totalProductParams);
    const totalCount = totalProducts[0].count;

    // Update jumlah produk untuk setiap kategori
    const categoriesWithCounts = rows.map(category => {
      if (category.value === 'all') {
        return { ...category, count: totalCount };
      }
      return { ...category, count: categoryCounts[category.value] || 0 };
    });

    res.json(categoriesWithCounts);
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

// Get category by ID
router.get('/:id', async (req, res) => {
  try {
    const { user_id } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Check if user is a branch user
    const [branchUser] = await pool.query(
      'SELECT * FROM branches WHERE id = ? AND is_active = 1',
      [user_id]
    );

    let query = 'SELECT * FROM categories WHERE id = ?';
    const params = [req.params.id];

    if (branchUser.length > 0) {
      // User is a branch user - only allow access to categories they created themselves
      query += ' AND user_id = ?';
      params.push(user_id);
    } else {
      // Admin user - only allow access to their own categories
      query += ' AND user_id = ?';
      params.push(user_id);
    }

    const [rows] = await pool.query(query, params);
    if (rows.length === 0) {
      return res.status(404).json({ error: 'Category not found or access denied' });
    }
    res.json(rows[0]);
  } catch (error) {
    console.error('Error fetching category:', error);
    res.status(500).json({ error: 'Failed to fetch category' });
  }
});

// Create new category
router.post('/', async (req, res) => {
  const { value, label, icon, count, user_id } = req.body;

  if (!value || !label || !user_id) {
    return res.status(400).json({ error: 'Value, label, and user_id are required' });
  }

  try {
    // Branch users can now create their own categories

    const [result] = await pool.query(
      'INSERT INTO categories (value, label, icon, count, user_id) VALUES (?, ?, ?, ?, ?)',
      [value, label, icon || '', count || 0, user_id]
    );

    res.status(201).json({ id: result.insertId, ...req.body });
  } catch (error) {
    console.error('Error creating category:', error);
    res.status(500).json({ error: 'Failed to create category' });
  }
});

// Update category
router.put('/:id', async (req, res) => {
  const { value, label, icon, count, user_id } = req.body;

  if (!value || !label || !user_id) {
    return res.status(400).json({ error: 'Value, label, and user_id are required' });
  }

  try {
    // Cek apakah kategori ada dan milik user yang sama
    const [categories] = await pool.query(
      'SELECT * FROM categories WHERE id = ? AND user_id = ?',
      [req.params.id, user_id]
    );

    if (categories.length === 0) {
      return res.status(404).json({ error: 'Category not found or not authorized' });
    }

    await pool.query(
      'UPDATE categories SET value = ?, label = ?, icon = ?, count = ? WHERE id = ?',
      [value, label, icon || '', count || 0, req.params.id]
    );

    res.json({ id: req.params.id, ...req.body });
  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({ error: 'Failed to update category' });
  }
});

// Delete category
router.delete('/:id', async (req, res) => {
  try {
    const { user_id } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Cek apakah kategori ada dan milik user yang sama
    const [categories] = await pool.query(
      'SELECT * FROM categories WHERE id = ? AND user_id = ?',
      [req.params.id, user_id]
    );

    if (categories.length === 0) {
      return res.status(404).json({ error: 'Category not found or not authorized' });
    }

    // Jangan izinkan menghapus kategori 'all'
    if (categories[0].value === 'all') {
      return res.status(400).json({ error: 'Cannot delete the "all" category' });
    }

    await pool.query('DELETE FROM categories WHERE id = ?', [req.params.id]);
    res.json({ message: 'Category deleted successfully' });
  } catch (error) {
    console.error('Error deleting category:', error);
    res.status(500).json({ error: 'Failed to delete category' });
  }
});

module.exports = router;


