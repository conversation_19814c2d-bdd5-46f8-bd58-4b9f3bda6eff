@tailwind base;
@tailwind components;
@tailwind utilities;

@media print {
  body * {
    visibility: hidden;
  }

  .print\:block,
  .print\:block * {
    visibility: visible;
  }

  .print\:block {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
  }
}

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
  /* Primary colors - Traveloka Blue */
  --color-primary-50: #f0f7ff;
  --color-primary-100: #e0effe;
  --color-primary-200: #bae0fd;
  --color-primary-300: #90cafc;
  --color-primary-400: #5aacf9;
  --color-primary-500: #2d8df6;
  --color-primary-600: #0070eb;
  --color-primary-700: #0058c6;
  --color-primary-800: #0047a0;
  --color-primary-900: #003979;

  /* Accent colors */
  --color-accent-50: #fff8f0;
  --color-accent-100: #ffe9d1;
  --color-accent-200: #ffd1a3;
  --color-accent-300: #ffb771;
  --color-accent-400: #ff9c42;
  --color-accent-500: #ff8c24;
  --color-accent-600: #e67200;
  --color-accent-700: #b35800;
  --color-accent-800: #804000;
  --color-accent-900: #4d2600;

  /* Neutral colors */
  --color-neutral-50: #f9fafb;
  --color-neutral-100: #f0f2f5;
  --color-neutral-200: #e2e7ed;
  --color-neutral-300: #d0d7e1;
  --color-neutral-400: #a4aebf;
  --color-neutral-500: #7b869d;
  --color-neutral-600: #5a657b;
  --color-neutral-700: #3f4859;
  --color-neutral-800: #252d3c;
  --color-neutral-900: #0f1623;

  /* Status colors */
  --color-success-500: #10b981;
  --color-warning-500: #f59e0b;
  --color-error-500: #ef4444;
}

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
      Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.5;
    color: var(--color-neutral-800);
    background-color: var(--color-neutral-50);
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    line-height: 1.2;
    color: var(--color-primary-800);
  }
}

@layer components {

  /* Custom component styles */
  .btn {
    @apply rounded-md font-medium transition-all duration-300 transform;
  }

  .btn-primary {
    @apply bg-[var(--color-primary-600)] hover:bg-[var(--color-primary-700)] text-white px-4 py-2;
  }

  .btn-secondary {
    @apply bg-[var(--color-accent-500)] hover:bg-[var(--color-accent-600)] text-white px-4 py-2;
  }

  .btn-outline {
    @apply border border-[var(--color-primary-600)] text-[var(--color-primary-600)] hover:bg-[var(--color-primary-50)] px-4 py-2;
  }

  .btn-danger {
    @apply bg-red-100 text-red-700 hover:bg-red-200 px-4 py-2;
  }

  .card {
    @apply bg-white rounded-xl shadow-sm overflow-hidden transition-all duration-300;
  }

  .menu-card {
    @apply card hover:shadow-md hover:-translate-y-1;
  }

  .menu-card-img {
    @apply h-48 overflow-hidden relative;
  }

  .menu-card-img::after {
    content: '';
    @apply absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 transition-opacity duration-300;
  }

  .menu-card:hover .menu-card-img::after {
    @apply opacity-100;
  }

  .menu-card-content {
    @apply p-5;
  }

  .menu-card-title {
    @apply text-lg font-bold mb-1 text-[var(--color-primary-800)];
  }

  .menu-card-desc {
    @apply text-sm text-[var(--color-neutral-600)] mb-3 h-10 overflow-hidden;
  }

  .menu-card-footer {
    @apply flex justify-between items-center;
  }

  .menu-card-price {
    @apply text-[var(--color-accent-600)] font-bold;
  }

  .menu-card-btn {
    @apply flex items-center text-[var(--color-primary-600)] hover:text-[var(--color-primary-700)] transition-colors gap-1 font-medium;
  }

  .cart-item {
    @apply flex items-center justify-between py-3 border-b border-[var(--color-neutral-200)];
  }

  .cart-qty-btn {
    @apply p-1 text-[var(--color-neutral-500)] hover:text-[var(--color-primary-600)] transition-colors rounded-full hover:bg-[var(--color-primary-50)];
  }

  .cart-remove-btn {
    @apply p-1 text-[var(--color-neutral-500)] hover:text-[var(--color-error-500)] transition-colors rounded-full hover:bg-red-50;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-[var(--color-neutral-300)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-primary-500)] focus:border-[var(--color-primary-500)] transition-all duration-200;
  }

  .receipt-section {
    @apply bg-white rounded-xl shadow-lg p-5 sticky top-4 w-full max-w-md mx-auto;
  }

  .receipt-heading {
    @apply text-xl font-bold mb-4 border-b-2 border-[var(--color-accent-300)] pb-2 text-[var(--color-primary-800)];
  }

  .section-heading {
    @apply text-2xl font-bold mb-4 text-[var(--color-primary-800)] border-b-2 border-[var(--color-accent-300)] pb-2 flex items-center gap-2;
  }

  /* Empty state styling */
  .empty-state {
    @apply flex flex-col items-center justify-center py-10 text-[var(--color-neutral-500)];
  }

  /* Header styling */
  .main-header {
    @apply bg-[var(--color-primary-700)] text-white shadow-md sticky top-0 z-10;
  }

  /* Footer styling */
  .main-footer {
    @apply bg-[var(--color-primary-800)] text-white py-8 mt-8 print:hidden;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-pulse-once {
  animation: pulse 0.5s ease-in-out;
}

/* Popup animation */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.fixed {
  animation: fadeIn 0.3s ease-out;
}

.fixed>div {
  animation: slideUp 0.3s ease-out;
}

/* Tambahkan animasi untuk dropdown menu */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.2s ease-out forwards;
}

/* Tambahkan style untuk dropdown menu */
.ring-1 {
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
}

/* Tambahkan style untuk menu item hover */
.hover\:bg-neutral-100:hover {
  background-color: #f5f5f5;
}

.hover\:bg-red-50:hover {
  background-color: #fef2f2;
}

/* Tambahkan style untuk OrderPanel di mobile */
@media (max-width: 768px) {
  .order-panel-mobile {
    min-height: 500px;
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .order-items-container {
    flex: 1;
    min-height: 200px;
    overflow-y: auto;
  }
}

/* Pastikan konten tidak terpotong di mobile */
@media (max-width: 768px) {
  .main-content-container {
    height: auto;
    min-height: 100vh;
  }
}

/* Tambahkan style untuk modal detail transaksi */
@media print {
  body * {
    visibility: hidden;
  }

  .print-content,
  .print-content * {
    visibility: visible;
  }

  .print-content {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 1rem;
  }
}

/* Tambahkan breakpoint untuk layar sangat kecil */
@media (min-width: 480px) {
  .xs\:inline {
    display: inline;
  }
}

/* Style untuk modal pada mobile */
@media (max-width: 640px) {
  .fixed.inset-0.flex.items-center.justify-center.z-50 {
    align-items: flex-start;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .max-h-\[90vh\] {
    max-height: 85vh;
  }
}

/* Pastikan tabel dalam modal dapat di-scroll horizontal */
.overflow-x-auto {
  -webkit-overflow-scrolling: touch;
}

/* Tambahkan style untuk animasi loading */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Style untuk halaman manajemen pada mobile */
@media (max-width: 640px) {

  /* Tombol dengan lebar penuh pada mobile */
  .w-full.sm\:w-auto {
    width: 100%;
  }

  /* Margin yang lebih kecil pada mobile */
  .mb-6.gap-3 {
    margin-bottom: 1rem;
  }

  /* Pastikan tabel dapat di-scroll dengan baik */
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
  }

  /* Pastikan modal konfirmasi hapus tidak terlalu lebar */
  .fixed.inset-0.flex.items-center.justify-center.z-50 .max-w-md {
    max-width: 90%;
  }

  /* Ukuran font yang lebih kecil pada mobile */
  .text-2xl.font-bold.text-primary-800 {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

/* Style untuk tombol aksi pada tabel */
@media (max-width: 480px) {
  .flex.justify-end.gap-2 {
    gap: 0.25rem;
  }

  .p-2.text-primary-600,
  .p-2.text-red-600 {
    padding: 0.375rem;
  }
}

/* Tambahkan style untuk ProductGrid pada tablet dan laptop kecil */
@media (min-width: 768px) and (max-width: 1280px) {

  /* Pastikan card produk memiliki ukuran yang konsisten */
  .product-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .product-card-image {
    height: 160px;
  }

  /* Pastikan teks kategori tidak terlalu panjang */
  .category-label {
    max-width: 100px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* Tambahkan class untuk membatasi jumlah baris teks */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}