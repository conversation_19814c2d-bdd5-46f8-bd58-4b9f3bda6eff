import React, { useState, useEffect } from 'react';
import { ArrowLeft, BarChart2, Download, Calendar, TrendingUp, DollarSign, FileText } from 'lucide-react';
import { Link } from 'react-router-dom';
import * as XLSX from 'xlsx';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import config from '../config';
import { fetchWithSession, getCurrentUser } from '../utils/api';

// Tambahkan interface untuk Branch
interface Branch {
  id: number;
  name: string;
}

interface TransactionItem {
  transaction_id: string;
  product_id: number;
  product_name: string;
  price: number;
  cost_price: number; // cost_price sudah disimpan di tabel transaction_items
  quantity: number;
}

interface Transaction {
  id: string;
  date: string;
  customer_name: string;
  branch_id: number;
  branch_name?: string;
  subtotal: number;
  tax: number;
  total: number;
  payment_method: string;
  amount_paid: number;
  change_amount: number;
  items?: TransactionItem[]; // Tambahkan items untuk menghitung profit,
  payment_status: 'paid' | 'pending' | 'cancelled'; // Tambahkan status pembayaran
}

interface DailyReport {
  date: string;
  formattedDate: string;
  totalSales: number;
  transactionCount: number;
  averageTransaction: number;
  totalProfit: number; // Tambahkan totalProfit
}

const ReportsPage: React.FC = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [dailyReports, setDailyReports] = useState<DailyReport[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().setDate(new Date().getDate() - 30)).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [totalRevenue, setTotalRevenue] = useState(0);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [averageTransactionValue, setAverageTransactionValue] = useState(0);
  const [totalProfit, setTotalProfit] = useState(0);

  // Tambahkan state untuk filter cabang
  const [branches, setBranches] = useState<Branch[]>([]);
  const [selectedBranchId, setSelectedBranchId] = useState<string>('');
  const [storeConfig, setStoreConfig] = useState<any>(null);

  // Get current user info
  const currentUser = getCurrentUser();
  const isAdmin = currentUser?.role === 'admin';
  const branchId = currentUser?.id;

  // Fungsi untuk mengambil data cabang
  const fetchBranches = async () => {
    try {
      // Tambahkan user_id sebagai query parameter untuk hanya mengambil cabang milik admin ini
      const response = await fetchWithSession(`${config.apiUrl}/branches?user_id=${currentUser.id}`);
      if (response && response.ok) {
        const data = await response.json();
        setBranches(data);
      }
    } catch (error) { }
  };

  // Fungsi untuk mengambil konfigurasi toko
  const fetchStoreConfig = async () => {
    try {
      const response = await fetchWithSession(`${config.apiUrl}/store-config?user_id=${currentUser.id}`);
      if (response && response.ok) {
        const data = await response.json();
        setStoreConfig(data);
      }
    } catch (error) {
      console.error('Error fetching store config:', error);
    }
  };

  useEffect(() => {
    const fetchTransactions = async () => {
      setIsLoading(true);
      try {
        // Tentukan URL berdasarkan role user dan filter cabang
        let url = `${config.apiUrl}/transactions`;

        if (!isAdmin) {
          // Jika bukan admin (cabang), hanya tampilkan transaksi cabang sendiri
          url = `${config.apiUrl}/transactions/branch/${branchId}`;
        } else if (isAdmin && selectedBranchId) {
          // Jika admin dan ada filter cabang yang dipilih
          url = `${config.apiUrl}/transactions/branch/${selectedBranchId}`;
        } else if (isAdmin) {
          // Jika admin tanpa filter cabang, tampilkan transaksi admin dan cabang-cabangnya
          url = `${config.apiUrl}/transactions?user_id=${currentUser.id}`;
        }

        // Tambahkan parameter tanggal jika ada
        if (dateRange.startDate && dateRange.endDate) {
          const startDate = formatDateForAPI(dateRange.startDate);
          const endDate = formatDateForAPI(dateRange.endDate);
          url += `${url.includes('?') ? '&' : '?'}start_date=${startDate}&end_date=${endDate}`;
        }

        const response = await fetchWithSession(url);
        if (response && response.ok) {
          const data = await response.json();

          // Ambil detail item untuk setiap transaksi
          const transactionsWithItems = await Promise.all(
            data.map(async (transaction: Transaction) => {
              try {
                const itemsResponse = await fetchWithSession(`${config.apiUrl}/transactions/${transaction.id}`);
                if (itemsResponse && itemsResponse.ok) {
                  const detailData = await itemsResponse.json();
                  return {
                    ...transaction,
                    items: detailData.items || []
                  };
                }
                return transaction;
              } catch (error) {
                return transaction;
              }
            })
          );

          // Filter hanya transaksi dengan status 'paid'
          const paidTransactions = transactionsWithItems.filter(
            (transaction: Transaction) => transaction.payment_status === 'paid'
          );

          setTransactions(paidTransactions);
        } else {
          setTransactions([]);
        }
      } catch (error) {
        setTransactions([]);
      } finally {
        setIsLoading(false);
      }
    };

    // Ambil data cabang hanya jika user adalah admin
    if (isAdmin) {
      fetchBranches();
    }

    // Ambil konfigurasi toko
    fetchStoreConfig();

    fetchTransactions();
  }, [isAdmin, branchId, selectedBranchId, dateRange]); // Tambahkan dateRange sebagai dependency

  // Fungsi untuk menangani perubahan filter cabang
  const handleBranchFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedBranchId(e.target.value);
  };

  useEffect(() => {
    if (transactions.length > 0) {
      generateDailyReports();
    } else {
      // Reset reports and totals if no transactions
      setDailyReports([]);
      setTotalRevenue(0);
      setTotalTransactions(0);
      setAverageTransactionValue(0);
      setTotalProfit(0); // Reset total profit
    }
  }, [transactions, dateRange]);

  const generateDailyReports = () => {
    // Filter transactions based on date range and payment status
    const filteredTransactions = transactions.filter(transaction => {
      // Pastikan hanya transaksi dengan status 'paid' yang dihitung
      if (transaction.payment_status !== 'paid') {
        return false;
      }

      // Ekstrak tanggal dari datetime (baik format dengan T maupun dengan spasi)
      let transactionDate;
      if (transaction.date.includes('T')) {
        transactionDate = transaction.date.split('T')[0]; // Format ISO: 2025-05-30T12:34:56
      } else {
        transactionDate = transaction.date.split(' ')[0]; // Format: 2025-05-30 12:34:56
      }

      // Pastikan format tanggal konsisten (YYYY-MM-DD)
      const startDate = dateRange.startDate;
      const endDate = dateRange.endDate;

      // Bandingkan tanggal sebagai string (YYYY-MM-DD format akan bekerja dengan benar untuk perbandingan)
      // Gunakan >= dan <= untuk memastikan tanggal awal dan akhir termasuk
      return transactionDate >= startDate && transactionDate <= endDate;
    });

    // Group transactions by date
    const groupedByDate: Record<string, Transaction[]> = {};

    filteredTransactions.forEach(transaction => {
      // Ekstrak tanggal dari datetime (baik format dengan T maupun dengan spasi)
      let date;
      if (transaction.date.includes('T')) {
        date = transaction.date.split('T')[0];
      } else {
        date = transaction.date.split(' ')[0];
      }

      if (!groupedByDate[date]) {
        groupedByDate[date] = [];
      }
      groupedByDate[date].push(transaction);
    });

    // Create daily reports
    const reports: DailyReport[] = [];

    Object.keys(groupedByDate).sort().forEach(date => {
      const dailyTransactions = groupedByDate[date];

      // Pastikan nilai total adalah angka yang valid
      const totalSales = dailyTransactions.reduce((sum, transaction) => {
        // Pastikan transaction.total adalah angka
        const total = typeof transaction.total === 'number' ? transaction.total :
          typeof transaction.total === 'string' ? parseFloat(transaction.total) : 0;
        return sum + total;
      }, 0);

      // Hitung total profit (pendapatan bersih)
      const totalProfit = dailyTransactions.reduce((sum, transaction) => {
        // Jika tidak ada items, tidak bisa menghitung profit
        if (!transaction.items || !Array.isArray(transaction.items)) {
          return sum;
        }

        // Hitung profit untuk setiap item dalam transaksi
        const transactionProfit = transaction.items.reduce((itemSum, item) => {
          const price = typeof item.price === 'number' ? item.price :
            typeof item.price === 'string' ? parseFloat(item.price) : 0;

          // Gunakan cost_price yang sudah disimpan di tabel transaction_items
          const costPrice = typeof item.cost_price === 'number' ? item.cost_price :
            typeof item.cost_price === 'string' ? parseFloat(item.cost_price) : 0;

          const quantity = typeof item.quantity === 'number' ? item.quantity :
            typeof item.quantity === 'string' ? parseFloat(item.quantity) : 1;

          // Profit per item = (harga jual - harga modal) * jumlah
          const itemProfit = (price - costPrice) * quantity;
          return itemSum + itemProfit;
        }, 0);

        return sum + transactionProfit;
      }, 0);

      const transactionCount = dailyTransactions.length;
      // Hindari pembagian dengan nol
      const averageTransaction = transactionCount > 0 ? totalSales / transactionCount : 0;

      reports.push({
        date,
        formattedDate: formatDate(date),
        totalSales,
        transactionCount,
        averageTransaction,
        totalProfit // Tambahkan total profit ke report
      });
    });

    // Calculate totals
    const totalRev = reports.reduce((sum, report) => {
      // Pastikan report.totalSales adalah angka
      const totalSales = typeof report.totalSales === 'number' ? report.totalSales : 0;
      return sum + totalSales;
    }, 0);

    const totalTrans = reports.reduce((sum, report) => sum + report.transactionCount, 0);

    // Hitung total profit dari semua laporan harian
    const totalProft = reports.reduce((sum, report) => {
      const profit = typeof report.totalProfit === 'number' ? report.totalProfit : 0;
      return sum + profit;
    }, 0);

    // Hindari pembagian dengan nol
    const avgTransaction = totalTrans > 0 ? totalRev / totalTrans : 0;

    setDailyReports(reports);
    setTotalRevenue(totalRev);
    setTotalTransactions(totalTrans);
    setAverageTransactionValue(avgTransaction);
    setTotalProfit(totalProft); // Set total profit
  };

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { day: '2-digit', month: 'long', year: 'numeric' };
    return new Date(dateString).toLocaleDateString('id-ID', options);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(price);
  };

  const handleExportData = () => {
    // Persiapkan data untuk export Excel
    const exportData = dailyReports.map(report => ({
      'Tanggal': report.formattedDate,
      'Total Penjualan': formatPrice(report.totalSales),
      'Jumlah Transaksi': report.transactionCount,
      'Rata-rata Transaksi': formatPrice(report.averageTransaction),
      'Total Profit': formatPrice(report.totalProfit)
    }));

    // Tambahkan baris ringkasan
    exportData.push({
      'Tanggal': 'TOTAL',
      'Total Penjualan': formatPrice(totalRevenue),
      'Jumlah Transaksi': totalTransactions,
      'Rata-rata Transaksi': formatPrice(averageTransactionValue),
      'Total Profit': formatPrice(totalProfit)
    });

    // Buat workbook baru
    const wb = XLSX.utils.book_new();

    // Buat worksheet dari data
    const ws = XLSX.utils.json_to_sheet(exportData);

    // Tambahkan worksheet ke workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Laporan Harian');

    // Tentukan nama file dengan informasi cabang
    let fileName = 'laporan';

    // Tambahkan informasi cabang ke nama file
    if (isAdmin) {
      if (selectedBranchId) {
        const selectedBranch = branches.find(b => b.id.toString() === selectedBranchId);
        fileName += `-${selectedBranch?.name || 'cabang-terpilih'}`;
      } else {
        fileName += '-semua-cabang';
      }
    } else {
      fileName += `-${currentUser?.name || 'cabang'}`;
    }

    // Tambahkan rentang tanggal ke nama file
    fileName += `-${dateRange.startDate}-hingga-${dateRange.endDate}.xlsx`;

    // Export file Excel
    XLSX.writeFile(wb, fileName);
  };

  const handleExportPDF = () => {
    try {
      const doc = new jsPDF();

      // Header dengan kop dokumen
      doc.setFontSize(18);
      doc.text(storeConfig?.store_name || 'Bakery POS', 105, 20, { align: 'center' });

      if (storeConfig?.store_address) {
        doc.setFontSize(12);
        doc.text(storeConfig.store_address, 105, 28, { align: 'center' });
      }

      if (storeConfig?.store_phone) {
        doc.setFontSize(10);
        doc.text(`Tel: ${storeConfig.store_phone}`, 105, 35, { align: 'center' });
      }

      // Garis pemisah
      doc.setLineWidth(0.5);
      doc.line(20, 40, 190, 40);

      // Judul laporan
      doc.setFontSize(16);
      doc.text('LAPORAN PENDAPATAN', 105, 50, { align: 'center' });

      // Info periode dan cabang
      doc.setFontSize(10);
      const periodText = `Periode: ${formatDate(dateRange.startDate)} - ${formatDate(dateRange.endDate)}`;
      doc.text(periodText, 20, 60);

      let branchText = '';
      if (isAdmin) {
        if (selectedBranchId) {
          const selectedBranch = branches.find(b => b.id.toString() === selectedBranchId);
          branchText = `Cabang: ${selectedBranch?.name || 'Cabang Terpilih'}`;
        } else {
          branchText = 'Cabang: Semua Cabang';
        }
      } else {
        branchText = `Cabang: ${currentUser?.name || 'Cabang'}`;
      }
      doc.text(branchText, 20, 67);

      // Ringkasan
      doc.setFontSize(12);
      doc.text('RINGKASAN', 20, 80);
      doc.setFontSize(10);
      doc.text(`Total Pendapatan: ${formatPrice(totalRevenue)}`, 20, 90);
      doc.text(`Total Transaksi: ${totalTransactions}`, 20, 97);
      doc.text(`Rata-rata Transaksi: ${formatPrice(averageTransactionValue)}`, 20, 104);
      doc.text(`Total Profit: ${formatPrice(totalProfit)}`, 20, 111);

      // Tabel data harian
      const tableColumn = ["Tanggal", "Penjualan", "Transaksi", "Rata-rata", "Profit"];
      const tableRows = dailyReports.map(report => [
        report.formattedDate,
        formatPrice(report.totalSales),
        report.transactionCount.toString(),
        formatPrice(report.averageTransaction),
        formatPrice(report.totalProfit)
      ]);

      // Tambahkan baris total
      tableRows.push([
        'TOTAL',
        formatPrice(totalRevenue),
        totalTransactions.toString(),
        formatPrice(averageTransactionValue),
        formatPrice(totalProfit)
      ]);

      autoTable(doc, {
        head: [tableColumn],
        body: tableRows,
        startY: 120,
        theme: 'grid',
        styles: {
          fontSize: 9,
          cellPadding: 3
        },
        headStyles: {
          fillColor: [66, 135, 245],
          textColor: 255,
          fontStyle: 'bold'
        },
        footStyles: {
          fillColor: [240, 240, 240],
          fontStyle: 'bold'
        },
        didParseCell: function (data) {
          // Style untuk baris total
          if (data.row.index === tableRows.length - 1) {
            data.cell.styles.fillColor = [240, 240, 240];
            data.cell.styles.fontStyle = 'bold';
          }
        }
      });

      // Footer
      const finalY = (doc as any).lastAutoTable.finalY || 120;
      doc.setFontSize(8);
      doc.text(`Dicetak pada: ${new Date().toLocaleString('id-ID')}`, 20, finalY + 20);
      doc.text('Laporan ini dibuat secara otomatis oleh sistem POS', 105, finalY + 30, { align: 'center' });

      // Tentukan nama file
      let fileName = 'laporan-pendapatan';
      if (isAdmin) {
        if (selectedBranchId) {
          const selectedBranch = branches.find(b => b.id.toString() === selectedBranchId);
          fileName += `-${selectedBranch?.name || 'cabang-terpilih'}`;
        } else {
          fileName += '-semua-cabang';
        }
      } else {
        fileName += `-${currentUser?.name || 'cabang'}`;
      }
      fileName += `-${dateRange.startDate}-hingga-${dateRange.endDate}.pdf`;

      // Save PDF
      doc.save(fileName);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Terjadi kesalahan saat membuat PDF');
    }
  };

  // Perbaiki fungsi handleDateRangeChange untuk memastikan format tanggal yang konsisten
  const handleDateRangeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    setDateRange(prev => ({
      ...prev,
      [name]: value
    }));
  };

  useEffect(() => {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - 30);

    // Format tanggal ke YYYY-MM-DD
    const formatDateToYYYYMMDD = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    const formattedStartDate = formatDateToYYYYMMDD(startDate);
    const formattedEndDate = formatDateToYYYYMMDD(endDate);

    setDateRange({
      startDate: formattedStartDate,
      endDate: formattedEndDate
    });
  }, []);

  return (
    <div className="container mx-auto p-4">
      {/* Header dengan judul dan tombol export yang responsif */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-3">
        <div className="flex items-center gap-3">
          <Link to="/dashboard-summary" className="p-2 rounded-full hover:bg-neutral-100">
            <ArrowLeft size={20} />
          </Link>
          <h1 className="text-xl sm:text-2xl font-bold text-primary-800">
            {isAdmin ? 'Laporan Pendapatan Semua Cabang' : `Laporan Pendapatan ${currentUser?.name || 'Cabang'}`}
          </h1>
        </div>
        <div className="flex gap-2">
          <button
            onClick={handleExportPDF}
            className="flex items-center justify-center gap-2 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          >
            <FileText size={18} />
            <span>Export PDF</span>
          </button>
          <button
            onClick={handleExportData}
            className="flex items-center justify-center gap-2 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          >
            <Download size={18} />
            <span>Export Excel</span>
          </button>
        </div>
      </div>

      {/* Filter tanggal dan cabang */}
      <div className="bg-white rounded-xl shadow-sm overflow-hidden mb-6">
        <div className="p-4 border-b border-neutral-200">
          <h2 className="text-lg font-medium text-neutral-800 mb-3">Filter Laporan</h2>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label htmlFor="startDate" className="block text-sm font-medium text-neutral-700 mb-1">
                  Tanggal Mulai
                </label>
                <input
                  type="date"
                  id="startDate"
                  name="startDate"
                  className="p-2 border border-neutral-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary-500"
                  value={dateRange.startDate}
                  onChange={handleDateRangeChange}
                  autoComplete="off"
                />
              </div>
              <div>
                <label htmlFor="endDate" className="block text-sm font-medium text-neutral-700 mb-1">
                  Tanggal Akhir
                </label>
                <input
                  type="date"
                  id="endDate"
                  name="endDate"
                  className="p-2 border border-neutral-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary-500"
                  value={dateRange.endDate}
                  onChange={handleDateRangeChange}
                  autoComplete="off"
                />
              </div>
            </div>

            {/* Filter Cabang (hanya untuk admin) */}
            {isAdmin && (
              <div className="md:w-64">
                <label htmlFor="branchFilter" className="block text-sm font-medium text-neutral-700 mb-1">
                  Cabang
                </label>
                <div className="relative">
                  <select
                    id="branchFilter"
                    className="p-2 border border-neutral-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary-500 appearance-none"
                    value={selectedBranchId}
                    onChange={handleBranchFilterChange}
                  >
                    <option value="">Semua Cabang</option>
                    {branches.map(branch => (
                      <option key={branch.id} value={branch.id}>
                        {branch.name}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-neutral-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Kartu statistik */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-neutral-600 font-medium">Total Pendapatan</h2>
            <div className="p-2 bg-green-100 rounded-full">
              <DollarSign size={20} className="text-green-600" />
            </div>
          </div>
          <p className="text-2xl font-bold text-neutral-800">
            {formatPrice(isNaN(totalRevenue) ? 0 : totalRevenue)}
          </p>
          <p className="text-sm text-neutral-500">Periode {formatDate(dateRange.startDate)} - {formatDate(dateRange.endDate)}</p>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-neutral-600 font-medium">Pendapatan Bersih</h2>
            <div className="p-2 bg-blue-100 rounded-full">
              <TrendingUp size={20} className="text-blue-600" />
            </div>
          </div>
          <p className="text-2xl font-bold text-neutral-800">
            {formatPrice(isNaN(totalProfit) ? 0 : totalProfit)}
          </p>
          <p className="text-sm text-neutral-500">Periode {formatDate(dateRange.startDate)} - {formatDate(dateRange.endDate)}</p>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-neutral-600 font-medium">Jumlah Transaksi</h2>
            <div className="p-2 bg-purple-100 rounded-full">
              <BarChart2 size={20} className="text-purple-600" />
            </div>
          </div>
          <p className="text-2xl font-bold text-neutral-800">{totalTransactions}</p>
          <p className="text-sm text-neutral-500">Periode {formatDate(dateRange.startDate)} - {formatDate(dateRange.endDate)}</p>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-neutral-600 font-medium">Rata-rata Transaksi</h2>
            <div className="p-2 bg-amber-100 rounded-full">
              <Calendar size={20} className="text-amber-600" />
            </div>
          </div>
          <p className="text-2xl font-bold text-neutral-800">
            {formatPrice(isNaN(averageTransactionValue) ? 0 : averageTransactionValue)}
          </p>
          <p className="text-sm text-neutral-500">Periode {formatDate(dateRange.startDate)} - {formatDate(dateRange.endDate)}</p>
        </div>
      </div>

      {/* Tabel laporan */}
      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="p-4 border-b border-neutral-200">
          <h2 className="text-lg font-medium text-neutral-800">Laporan Harian</h2>
        </div>

        {isLoading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary-500 border-t-transparent"></div>
            <p className="mt-2 text-neutral-600">Memuat data laporan...</p>
          </div>
        ) : dailyReports.length === 0 ? (
          <div className="text-center py-8 text-neutral-500">
            <p>Tidak ada data transaksi untuk periode ini</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full min-w-[650px]">
              <thead className="bg-neutral-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Tanggal</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider">Jumlah Transaksi</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider">Total Pendapatan</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider">Pendapatan Bersih</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider">Rata-rata Transaksi</th>
                </tr>
              </thead>
              <tbody>
                {dailyReports.map((report) => (
                  <tr key={report.date} className="border-b border-neutral-100 hover:bg-neutral-50">
                    <td className="px-4 py-3 font-medium text-neutral-800">{report.formattedDate}</td>
                    <td className="px-4 py-3 text-right text-neutral-600">{report.transactionCount}</td>
                    <td className="px-4 py-3 text-right font-medium text-neutral-800">
                      {formatPrice(isNaN(report.totalSales) ? 0 : report.totalSales)}
                    </td>
                    <td className="px-4 py-3 text-right font-medium text-green-600">
                      {formatPrice(isNaN(report.totalProfit) ? 0 : report.totalProfit)}
                    </td>
                    <td className="px-4 py-3 text-right text-neutral-600">
                      {formatPrice(isNaN(report.averageTransaction) ? 0 : report.averageTransaction)}
                    </td>
                  </tr>
                ))}
                <tr className="bg-neutral-50">
                  <td className="px-4 py-3 font-bold text-neutral-800">TOTAL</td>
                  <td className="px-4 py-3 text-right font-bold text-neutral-800">{totalTransactions}</td>
                  <td className="px-4 py-3 text-right font-bold text-neutral-800">
                    {formatPrice(isNaN(totalRevenue) ? 0 : totalRevenue)}
                  </td>
                  <td className="px-4 py-3 text-right font-bold text-green-600">
                    {formatPrice(isNaN(totalProfit) ? 0 : totalProfit)}
                  </td>
                  <td className="px-4 py-3 text-right font-bold text-neutral-800">
                    {formatPrice(isNaN(averageTransactionValue) ? 0 : averageTransactionValue)}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReportsPage;

// Fungsi untuk memformat tanggal untuk API
const formatDateForAPI = (dateString: string): string => {
  // Pastikan format tanggal adalah YYYY-MM-DD
  // Jika sudah dalam format yang benar, kembalikan langsung
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
    return dateString;
  }

  // Jika format lain, konversi ke YYYY-MM-DD
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};





