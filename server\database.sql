/*
Navicat MySQL Data Transfer

Source Server         : localhost
Source Server Version : 50505
Source Host           : localhost:3306
Source Database       : satulisan

Target Server Type    : MYSQL
Target Server Version : 50505
File Encoding         : 65001

Date: 2025-06-17 10:34:43
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for branches
-- ----------------------------
DROP TABLE IF EXISTS `branches`;
CREATE TABLE `branches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `manager` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `email` varchar(100) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of branches
-- ----------------------------

-- ----------------------------
-- Table structure for cashiers
-- ----------------------------
DROP TABLE IF EXISTS `cashiers`;
CREATE TABLE `cashiers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of cashiers
-- ----------------------------
INSERT INTO `cashiers` VALUES ('1', 'Moch Arizal Fauzi', '<EMAIL>', 'owr216he890', null, '1', '1', '2025-06-03 12:11:02', '2025-06-03 12:11:02');

-- ----------------------------
-- Table structure for categories
-- ----------------------------
DROP TABLE IF EXISTS `categories`;
CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `value` varchar(50) DEFAULT NULL,
  `label` varchar(100) DEFAULT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `count` int(11) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of categories
-- ----------------------------
INSERT INTO `categories` VALUES ('1', 'makanan', 'Makanan', '?', '0', '2025-06-02 16:22:46', '2025-06-02 16:22:46', '1');
INSERT INTO `categories` VALUES ('2', 'minuman', 'Minuman', '?', '0', '2025-06-02 16:23:00', '2025-06-02 16:23:00', '1');
INSERT INTO `categories` VALUES ('3', 'snack', 'Snack', '?', '0', '2025-06-02 16:23:24', '2025-06-02 16:23:24', '1');

-- ----------------------------
-- Table structure for members
-- ----------------------------
DROP TABLE IF EXISTS `members`;
CREATE TABLE `members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `birthdate` date DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `notes` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of members
-- ----------------------------

-- ----------------------------
-- Table structure for msusers
-- ----------------------------
DROP TABLE IF EXISTS `msusers`;
CREATE TABLE `msusers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `role` varchar(20) DEFAULT 'admin',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of msusers
-- ----------------------------
INSERT INTO `msusers` VALUES ('1', 'Satu Lisan', '<EMAIL>', 'owr216he890', 'admin', '1', '2025-05-29 20:48:46', '2025-06-02 15:56:01');

-- ----------------------------
-- Table structure for payment_gateways
-- ----------------------------
DROP TABLE IF EXISTS `payment_gateways`;
CREATE TABLE `payment_gateways` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider` varchar(50) DEFAULT NULL,
  `is_production` tinyint(1) DEFAULT 0,
  `client_key` varchar(255) DEFAULT NULL,
  `server_key` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of payment_gateways
-- ----------------------------
INSERT INTO `payment_gateways` VALUES ('1', 'midtrans', '0', 'SB-Mid-client-qPWFjzplfWLiqwC_', 'SB-Mid-server-35Tw59xovs6dMDWb5_4gTXUb', '1', '2025-06-17 09:54:16', '2025-06-17 09:54:16', '1');

-- ----------------------------
-- Table structure for payment_gateway_transactions
-- ----------------------------
DROP TABLE IF EXISTS `payment_gateway_transactions`;
CREATE TABLE `payment_gateway_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` varchar(255) DEFAULT NULL,
  `gateway_transaction_id` varchar(255) DEFAULT NULL,
  `gateway_provider` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `payment_type` varchar(50) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `response_data` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of payment_gateway_transactions
-- ----------------------------
INSERT INTO `payment_gateway_transactions` VALUES ('1', 'TRX-1750128651000', 'TRX-1750128651000-1750128882551', 'midtrans', '16650.00', 'qris', 'pending', '{\"status_code\":\"201\",\"status_message\":\"Gopay transaction is created\",\"transaction_id\":\"556241d5-5821-4e51-9bd1-41f57ef15702\",\"order_id\":\"TRX-1750128651000-1750128882551\",\"merchant_id\":\"G601592270\",\"gross_amount\":\"16650.00\",\"currency\":\"IDR\",\"payment_type\":\"gopay\",\"transaction_time\":\"2025-06-17 09:54:42\",\"transaction_status\":\"pending\",\"fraud_status\":\"accept\",\"actions\":[{\"name\":\"generate-qr-code\",\"method\":\"GET\",\"url\":\"https://api.sandbox.midtrans.com/v2/gopay/556241d5-5821-4e51-9bd1-41f57ef15702/qr-code\"},{\"name\":\"deeplink-redirect\",\"method\":\"GET\",\"url\":\"https://simulator.sandbox.midtrans.com/v2/deeplink/detail?tref=A120250617025442KPO5EQLIZRID\"},{\"name\":\"get-status\",\"method\":\"GET\",\"url\":\"https://api.sandbox.midtrans.com/v2/556241d5-5821-4e51-9bd1-41f57ef15702/status\"},{\"name\":\"cancel\",\"method\":\"POST\",\"url\":\"https://api.sandbox.midtrans.com/v2/556241d5-5821-4e51-9bd1-41f57ef15702/cancel\"}],\"expiry_time\":\"2025-06-17 10:09:41\"}', '2025-06-17 09:54:42', '2025-06-17 09:54:42');
INSERT INTO `payment_gateway_transactions` VALUES ('2', 'TRX-1750129375000', 'TRX-1750129375000-1750129379860', 'midtrans', '16650.00', 'qris', 'pending', '{\"status_code\":\"201\",\"status_message\":\"Gopay transaction is created\",\"transaction_id\":\"8b69b43b-cff9-4556-b3fa-a713daf8b2e7\",\"order_id\":\"TRX-1750129375000-1750129379860\",\"merchant_id\":\"G601592270\",\"gross_amount\":\"16650.00\",\"currency\":\"IDR\",\"payment_type\":\"gopay\",\"transaction_time\":\"2025-06-17 10:02:59\",\"transaction_status\":\"pending\",\"fraud_status\":\"accept\",\"actions\":[{\"name\":\"generate-qr-code\",\"method\":\"GET\",\"url\":\"https://api.sandbox.midtrans.com/v2/gopay/8b69b43b-cff9-4556-b3fa-a713daf8b2e7/qr-code\"},{\"name\":\"deeplink-redirect\",\"method\":\"GET\",\"url\":\"https://simulator.sandbox.midtrans.com/v2/deeplink/detail?tref=A120250617030259cqgRXU4I7aID\"},{\"name\":\"get-status\",\"method\":\"GET\",\"url\":\"https://api.sandbox.midtrans.com/v2/8b69b43b-cff9-4556-b3fa-a713daf8b2e7/status\"},{\"name\":\"cancel\",\"method\":\"POST\",\"url\":\"https://api.sandbox.midtrans.com/v2/8b69b43b-cff9-4556-b3fa-a713daf8b2e7/cancel\"}],\"expiry_time\":\"2025-06-17 10:17:59\"}', '2025-06-17 10:03:00', '2025-06-17 10:03:00');
INSERT INTO `payment_gateway_transactions` VALUES ('3', 'TRX-1750129445000', 'TRX-1750129445000-1750129641865', 'midtrans', '16650.00', 'qris', 'pending', '{\"status_code\":\"201\",\"status_message\":\"Gopay transaction is created\",\"transaction_id\":\"78af91f0-ece7-46c7-abbd-a2f819ce7860\",\"order_id\":\"TRX-1750129445000-1750129641865\",\"merchant_id\":\"G601592270\",\"gross_amount\":\"16650.00\",\"currency\":\"IDR\",\"payment_type\":\"gopay\",\"transaction_time\":\"2025-06-17 10:07:21\",\"transaction_status\":\"pending\",\"fraud_status\":\"accept\",\"actions\":[{\"name\":\"generate-qr-code\",\"method\":\"GET\",\"url\":\"https://api.sandbox.midtrans.com/v2/gopay/78af91f0-ece7-46c7-abbd-a2f819ce7860/qr-code\"},{\"name\":\"deeplink-redirect\",\"method\":\"GET\",\"url\":\"https://simulator.sandbox.midtrans.com/v2/deeplink/detail?tref=A120250617030721gFYZLsUqM3ID\"},{\"name\":\"get-status\",\"method\":\"GET\",\"url\":\"https://api.sandbox.midtrans.com/v2/78af91f0-ece7-46c7-abbd-a2f819ce7860/status\"},{\"name\":\"cancel\",\"method\":\"POST\",\"url\":\"https://api.sandbox.midtrans.com/v2/78af91f0-ece7-46c7-abbd-a2f819ce7860/cancel\"}],\"expiry_time\":\"2025-06-17 10:22:21\"}', '2025-06-17 10:07:22', '2025-06-17 10:07:22');
INSERT INTO `payment_gateway_transactions` VALUES ('4', 'TRX-1750129642000', 'TRX-1750129642000-1750129652982', 'midtrans', '16650.00', 'qris', 'pending', '{\"status_code\":\"201\",\"status_message\":\"Gopay transaction is created\",\"transaction_id\":\"3dce1492-0a0a-4258-a0ac-1cad69748345\",\"order_id\":\"TRX-1750129642000-1750129652982\",\"merchant_id\":\"G601592270\",\"gross_amount\":\"16650.00\",\"currency\":\"IDR\",\"payment_type\":\"gopay\",\"transaction_time\":\"2025-06-17 10:07:32\",\"transaction_status\":\"pending\",\"fraud_status\":\"accept\",\"actions\":[{\"name\":\"generate-qr-code\",\"method\":\"GET\",\"url\":\"https://api.sandbox.midtrans.com/v2/gopay/3dce1492-0a0a-4258-a0ac-1cad69748345/qr-code\"},{\"name\":\"deeplink-redirect\",\"method\":\"GET\",\"url\":\"https://simulator.sandbox.midtrans.com/v2/deeplink/detail?tref=A120250617030732mXuo0zBJ3KID\"},{\"name\":\"get-status\",\"method\":\"GET\",\"url\":\"https://api.sandbox.midtrans.com/v2/3dce1492-0a0a-4258-a0ac-1cad69748345/status\"},{\"name\":\"cancel\",\"method\":\"POST\",\"url\":\"https://api.sandbox.midtrans.com/v2/3dce1492-0a0a-4258-a0ac-1cad69748345/cancel\"}],\"expiry_time\":\"2025-06-17 10:22:32\"}', '2025-06-17 10:07:33', '2025-06-17 10:07:33');
INSERT INTO `payment_gateway_transactions` VALUES ('5', 'TRX-1750129783000', 'TRX-1750129783000-1750129831253', 'midtrans', '16650.00', 'qris', 'pending', '{\"status_code\":\"201\",\"status_message\":\"Gopay transaction is created\",\"transaction_id\":\"dd3cdbf6-4d62-45c9-af6b-a0d60b47cad2\",\"order_id\":\"TRX-1750129783000-1750129831253\",\"merchant_id\":\"G601592270\",\"gross_amount\":\"16650.00\",\"currency\":\"IDR\",\"payment_type\":\"gopay\",\"transaction_time\":\"2025-06-17 10:10:30\",\"transaction_status\":\"pending\",\"fraud_status\":\"accept\",\"actions\":[{\"name\":\"generate-qr-code\",\"method\":\"GET\",\"url\":\"https://api.sandbox.midtrans.com/v2/gopay/dd3cdbf6-4d62-45c9-af6b-a0d60b47cad2/qr-code\"},{\"name\":\"deeplink-redirect\",\"method\":\"GET\",\"url\":\"https://simulator.sandbox.midtrans.com/v2/deeplink/detail?tref=A120250617031030LeMOeSzbmmID\"},{\"name\":\"get-status\",\"method\":\"GET\",\"url\":\"https://api.sandbox.midtrans.com/v2/dd3cdbf6-4d62-45c9-af6b-a0d60b47cad2/status\"},{\"name\":\"cancel\",\"method\":\"POST\",\"url\":\"https://api.sandbox.midtrans.com/v2/dd3cdbf6-4d62-45c9-af6b-a0d60b47cad2/cancel\"}],\"expiry_time\":\"2025-06-17 10:25:30\"}', '2025-06-17 10:10:31', '2025-06-17 10:10:31');
INSERT INTO `payment_gateway_transactions` VALUES ('6', 'TRX-1750130826000', 'TRX-1750130826000-1750130829299', 'midtrans', '27750.00', 'qris', 'pending', '{\"status_code\":\"201\",\"status_message\":\"Gopay transaction is created\",\"transaction_id\":\"62046cdf-ba26-4b0a-98da-f9d4578717bc\",\"order_id\":\"TRX-1750130826000-1750130829299\",\"merchant_id\":\"G601592270\",\"gross_amount\":\"27750.00\",\"currency\":\"IDR\",\"payment_type\":\"gopay\",\"transaction_time\":\"2025-06-17 10:27:08\",\"transaction_status\":\"pending\",\"fraud_status\":\"accept\",\"actions\":[{\"name\":\"generate-qr-code\",\"method\":\"GET\",\"url\":\"https://api.sandbox.midtrans.com/v2/gopay/62046cdf-ba26-4b0a-98da-f9d4578717bc/qr-code\"},{\"name\":\"deeplink-redirect\",\"method\":\"GET\",\"url\":\"https://simulator.sandbox.midtrans.com/v2/deeplink/detail?tref=A120250617032708CjSLMJ2wNmID\"},{\"name\":\"get-status\",\"method\":\"GET\",\"url\":\"https://api.sandbox.midtrans.com/v2/62046cdf-ba26-4b0a-98da-f9d4578717bc/status\"},{\"name\":\"cancel\",\"method\":\"POST\",\"url\":\"https://api.sandbox.midtrans.com/v2/62046cdf-ba26-4b0a-98da-f9d4578717bc/cancel\"}],\"expiry_time\":\"2025-06-17 10:42:08\"}', '2025-06-17 10:27:09', '2025-06-17 10:27:09');

-- ----------------------------
-- Table structure for payment_methods
-- ----------------------------
DROP TABLE IF EXISTS `payment_methods`;
CREATE TABLE `payment_methods` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `type` enum('cash','bank_transfer','qris','card','e_wallet','other') DEFAULT 'other',
  `description` text DEFAULT NULL,
  `account_number` varchar(50) DEFAULT NULL,
  `account_name` varchar(100) DEFAULT NULL,
  `bank_name` varchar(100) DEFAULT NULL,
  `qris_image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `wallet_number` varchar(255) DEFAULT NULL,
  `wallet_provider` varchar(255) DEFAULT NULL,
  `use_gateway` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of payment_methods
-- ----------------------------
INSERT INTO `payment_methods` VALUES ('1', 'QRIS', 'qris', '', null, null, null, null, '1', '2025-06-17 09:50:47', '2025-06-17 09:50:47', null, null, '1', '1');

-- ----------------------------
-- Table structure for products
-- ----------------------------
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `category` varchar(50) DEFAULT NULL,
  `category_label` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_active` int(11) DEFAULT NULL,
  `cost_price` bigint(20) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_products_user` (`user_id`),
  KEY `fk_products_branch` (`branch_id`),
  CONSTRAINT `fk_products_user` FOREIGN KEY (`user_id`) REFERENCES `msusers` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_products_branch` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of products
-- ----------------------------
INSERT INTO `products` VALUES ('1', 'Nasi goreng', '25000.00', 'https://sanex.co.id/wp-content/uploads/2024/11/2734.jpg', 'makanan', 'Makanan', '2025-06-02 16:25:10', '2025-06-17 09:17:57', '1', '12000', '1');
INSERT INTO `products` VALUES ('2', 'Mie goreng', '25000.00', 'https://allofresh.id/blog/wp-content/uploads/2023/09/cara-membuat-mie-goreng-4-1-scaled.jpg', 'makanan', 'Makanan', '2025-06-02 16:26:10', '2025-06-17 09:18:12', '1', '10000', '1');
INSERT INTO `products` VALUES ('3', 'Juice Strawberry', '15000.00', 'https://brokebankvegan.com/wp-content/uploads/2022/08/Strawberry-Juice-10.jpg', 'minuman', 'Minuman', '2025-06-02 16:27:31', '2025-06-17 09:18:50', '1', '10000', '1');
INSERT INTO `products` VALUES ('4', 'Juice Melon', '15000.00', 'https://img-global.cpcdn.com/recipes/a2f74f991e52dfcf/680x482f0.5_0.520114_1.0q90/jus-melon-dan-strawberry-foto-resep-utama.jpg', 'minuman', 'Minuman', '2025-06-02 16:27:56', '2025-06-17 09:19:33', '1', '10000', '1');

-- ----------------------------
-- Table structure for store_config
-- ----------------------------
DROP TABLE IF EXISTS `store_config`;
CREATE TABLE `store_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `store_name` varchar(100) DEFAULT 'Bakery POS',
  `store_address` varchar(255) DEFAULT NULL,
  `store_phone` varchar(20) DEFAULT NULL,
  `store_email` varchar(100) DEFAULT NULL,
  `tax_percentage` decimal(5,2) DEFAULT 0.00,
  `currency` varchar(10) DEFAULT 'IDR',
  `logo` varchar(255) DEFAULT NULL,
  `receipt_footer` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of store_config
-- ----------------------------
INSERT INTO `store_config` VALUES ('1', 'Satu Lisan', null, null, null, '11.00', 'IDR', null, null, '2025-06-02 20:40:42', '2025-06-17 09:34:25', '1');

-- ----------------------------
-- Table structure for transactions
-- ----------------------------
DROP TABLE IF EXISTS `transactions`;
CREATE TABLE `transactions` (
  `id` varchar(50) NOT NULL,
  `date` datetime DEFAULT NULL,
  `customer_name` varchar(100) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `subtotal` decimal(10,2) DEFAULT NULL,
  `tax` decimal(10,2) DEFAULT 0.00,
  `total` decimal(10,2) DEFAULT NULL,
  `amount_paid` decimal(10,2) DEFAULT NULL,
  `change_amount` decimal(10,2) DEFAULT NULL,
  `payment_method` varchar(50) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `payment_method_id` int(11) DEFAULT NULL,
  `member_id` int(11) DEFAULT NULL,
  `payment_status` enum('paid','cancelled','pending') DEFAULT NULL,
  `outlet_id` int(11) DEFAULT NULL,
  `cashier_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of transactions
-- ----------------------------
INSERT INTO `transactions` VALUES ('TRX-1750128571000', '2025-06-17 09:49:35', 'Customer', null, '25000.00', '2750.00', '27750.00', '27750.00', '0.00', 'Cash', '2025-06-17 09:49:35', null, null, 'paid', null, '1');
INSERT INTO `transactions` VALUES ('TRX-1750128575000', '2025-06-17 09:50:05', 'Customer', null, '25000.00', '2750.00', '27750.00', '50000.00', '22250.00', 'Cash', '2025-06-17 09:50:05', null, null, 'paid', null, '1');
INSERT INTO `transactions` VALUES ('TRX-1750128651000', '2025-06-17 09:54:42', 'Customer', null, '15000.00', '1650.00', '16650.00', '16650.00', '0.00', 'QRIS', '2025-06-17 09:54:42', '1', null, 'pending', null, '1');
INSERT INTO `transactions` VALUES ('TRX-1750129375000', '2025-06-17 10:02:59', 'Customer', null, '15000.00', '1650.00', '16650.00', '16650.00', '0.00', 'QRIS', '2025-06-17 10:02:59', '1', null, 'pending', null, '1');
INSERT INTO `transactions` VALUES ('TRX-1750129445000', '2025-06-17 10:07:21', 'Customer', null, '15000.00', '1650.00', '16650.00', '16650.00', '0.00', 'QRIS', '2025-06-17 10:07:21', '1', null, 'pending', null, '1');
INSERT INTO `transactions` VALUES ('TRX-1750129642000', '2025-06-17 10:07:32', 'Customer', null, '15000.00', '1650.00', '16650.00', '16650.00', '0.00', 'QRIS', '2025-06-17 10:07:32', '1', null, 'pending', null, '1');
INSERT INTO `transactions` VALUES ('TRX-1750129653000', '2025-06-17 10:07:46', 'Customer', null, '15000.00', '1650.00', '16650.00', '50000.00', '33350.00', 'Cash', '2025-06-17 10:07:46', null, null, 'paid', null, '1');
INSERT INTO `transactions` VALUES ('TRX-1750129783000', '2025-06-17 10:10:31', 'Customer', null, '15000.00', '1650.00', '16650.00', '16650.00', '0.00', 'QRIS', '2025-06-17 10:10:31', '1', null, 'pending', null, '1');
INSERT INTO `transactions` VALUES ('TRX-1750130826000', '2025-06-17 10:27:09', 'Customer', null, '25000.00', '2750.00', '27750.00', '27750.00', '0.00', 'QRIS', '2025-06-17 10:27:09', '1', null, 'pending', null, '1');

-- ----------------------------
-- Table structure for transaction_items
-- ----------------------------
DROP TABLE IF EXISTS `transaction_items`;
CREATE TABLE `transaction_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` varchar(50) DEFAULT NULL,
  `product_name` varchar(100) DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `product_id` int(11) DEFAULT NULL,
  `cost_price` decimal(10,0) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=261 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of transaction_items
-- ----------------------------
INSERT INTO `transaction_items` VALUES ('251', 'TRX-1750128571000', 'Nasi goreng', '25000.00', '1', '2025-06-17 09:49:35', '1', '12000');
INSERT INTO `transaction_items` VALUES ('252', 'TRX-1750128575000', 'Mie goreng', '25000.00', '1', '2025-06-17 09:50:05', '2', '10000');
INSERT INTO `transaction_items` VALUES ('254', 'TRX-1750128651000', 'Juice Strawberry', '15000.00', '1', '2025-06-17 09:54:42', '3', '10000');
INSERT INTO `transaction_items` VALUES ('255', 'TRX-1750129375000', 'Juice Melon', '15000.00', '1', '2025-06-17 10:02:59', '4', '10000');
INSERT INTO `transaction_items` VALUES ('256', 'TRX-1750129445000', 'Juice Melon', '15000.00', '1', '2025-06-17 10:07:21', '4', '10000');
INSERT INTO `transaction_items` VALUES ('257', 'TRX-1750129642000', 'Juice Strawberry', '15000.00', '1', '2025-06-17 10:07:32', '3', '10000');
INSERT INTO `transaction_items` VALUES ('258', 'TRX-1750129653000', 'Juice Melon', '15000.00', '1', '2025-06-17 10:07:46', '4', '10000');
INSERT INTO `transaction_items` VALUES ('259', 'TRX-1750129783000', 'Juice Melon', '15000.00', '1', '2025-06-17 10:10:31', '4', '10000');
INSERT INTO `transaction_items` VALUES ('260', 'TRX-1750130826000', 'Nasi goreng', '25000.00', '1', '2025-06-17 10:27:09', '1', '12000');

-- ----------------------------
-- Table structure for expenses
-- ----------------------------
DROP TABLE IF EXISTS `expenses`;
CREATE TABLE `expenses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `description` varchar(255) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `category` varchar(100) DEFAULT NULL,
  `date` datetime NOT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fk_expenses_user` (`user_id`),
  KEY `fk_expenses_branch` (`branch_id`),
  KEY `idx_expenses_date` (`date`),
  KEY `idx_expenses_category` (`category`),
  CONSTRAINT `fk_expenses_user` FOREIGN KEY (`user_id`) REFERENCES `msusers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_expenses_branch` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of expenses
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
