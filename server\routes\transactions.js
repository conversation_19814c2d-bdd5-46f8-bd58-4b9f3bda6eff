const express = require('express');
const router = express.Router();
const pool = require('../config/database');
const Transaction = require('../models/Transaction');

// Get all transactions
router.get('/', async (req, res) => {
  try {
    const { user_id, start_date, end_date } = req.query;

    let query = `
      SELECT t.*,
             b.name as branch_name,
             pm.name as payment_method_name,
             pm.type as payment_method_type,
             c.name as cashier_name
      FROM transactions t
      LEFT JOIN branches b ON t.branch_id = b.id
      LEFT JOIN payment_methods pm ON t.payment_method_id = pm.id
      LEFT JOIN cashiers c ON t.cashier_id = c.id
    `;

    const params = [];
    const conditions = [];

    // Jika request dari kasir, tambahkan filter berdasarkan cashier_id
    if (req.query.cashier_id) {
      conditions.push('t.cashier_id = ?');
      params.push(req.query.cashier_id);
    }
    // Filter berdasarkan user_id (outlet_id) - hanya jika bukan request dari kasir
    else if (user_id) {
      // Tambahkan kondisi untuk outlet_id atau user_id di branches
      conditions.push('(t.outlet_id = ? OR b.user_id = ? OR c.user_id = ?)');
      params.push(user_id, user_id, user_id);
    }

    // Filter berdasarkan tanggal
    if (start_date && end_date) {
      // Tambahkan 1 hari ke end_date untuk memastikan tanggal akhir termasuk
      const endDateObj = new Date(end_date);
      endDateObj.setDate(endDateObj.getDate() + 1);
      const inclusiveEndDate = endDateObj.toISOString().split('T')[0];

      conditions.push('t.date >= ? AND t.date < ?');
      params.push(start_date, inclusiveEndDate);
    }

    // Tambahkan WHERE clause jika ada kondisi
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    // Tambahkan ORDER BY
    query += ' ORDER BY t.date DESC';

    const [rows] = await pool.query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('Error fetching transactions:', error);
    res.status(500).json({ error: 'Failed to fetch transactions' });
  }
});

// Get transactions by branch
router.get('/branch/:branchId', async (req, res) => {
  try {
    const { branchId } = req.params;
    const { start_date, end_date } = req.query;

    let query = `
      SELECT t.*, b.name as branch_name,
             pm.name as payment_method_name,
             pm.type as payment_method_type,
             c.name as cashier_name
      FROM transactions t
      LEFT JOIN branches b ON t.branch_id = b.id
      LEFT JOIN payment_methods pm ON t.payment_method_id = pm.id
      LEFT JOIN cashiers c ON t.cashier_id = c.id
      WHERE t.branch_id = ?
    `;

    const params = [branchId];

    // Tambahkan filter tanggal jika ada
    if (start_date && end_date) {
      // Tambahkan 1 hari ke end_date untuk memastikan tanggal akhir termasuk
      const endDateObj = new Date(end_date);
      endDateObj.setDate(endDateObj.getDate() + 1);
      const inclusiveEndDate = endDateObj.toISOString().split('T')[0];

      query += ' AND t.date >= ? AND t.date < ?';
      params.push(start_date, inclusiveEndDate);
    }

    // Tambahkan ORDER BY
    query += ' ORDER BY t.date DESC';

    const [rows] = await pool.query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('Error fetching branch transactions:', error);
    res.status(500).json({ error: 'Failed to fetch branch transactions' });
  }
});

// Get transaction by ID with items
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Get transaction with payment method details
    const [transactions] = await pool.query(`
      SELECT t.*, 
             b.name as branch_name, 
             pm.name as payment_method_name, 
             pm.type as payment_method_type,
             pm.bank_name,
             pm.account_number,
             pm.account_name,
             pm.qris_image AS qris_image_url,
             pm.wallet_provider,
             pm.wallet_number
      FROM transactions t
      LEFT JOIN branches b ON t.branch_id = b.id
      LEFT JOIN payment_methods pm ON t.payment_method_id = pm.id
      WHERE t.id = ?
    `, [id]);

    if (transactions.length === 0) {
      return res.status(404).json({ error: 'Transaction not found' });
    }

    // Get transaction items
    const [items] = await pool.query(`
      SELECT * FROM transaction_items WHERE transaction_id = ?
    `, [id]);

    // Get payment gateway data if exists
    const [gatewayData] = await pool.query(`
      SELECT * FROM payment_gateway_transactions WHERE transaction_id = ?
    `, [id]);

    // Parse gateway response data to extract QR code URL
    let gatewayInfo = null;
    if (gatewayData.length > 0) {
      const gateway = gatewayData[0];
      try {
        const responseData = JSON.parse(gateway.response_data);

        // Extract QR code URL from Midtrans response
        let qrCodeUrl = null;
        if (responseData.actions) {
          const qrAction = responseData.actions.find(action => action.name === 'generate-qr-code');
          qrCodeUrl = qrAction ? qrAction.url : null;
        }

        gatewayInfo = {
          gateway_transaction_id: gateway.gateway_transaction_id,
          gateway_provider: gateway.gateway_provider,
          payment_type: gateway.payment_type,
          status: gateway.status,
          qr_code_url: qrCodeUrl,
          expiry_time: responseData.expiry_time,
          transaction_status: responseData.transaction_status
        };
      } catch (error) {
        console.error('Error parsing gateway response data:', error);
      }
    }

    // Combine transaction with items and gateway data
    const transaction = {
      ...transactions[0],
      items: items,
      gateway_info: gatewayInfo
    };

    res.json(transaction);
  } catch (error) {
    console.error('Error fetching transaction:', error);
    res.status(500).json({ error: 'Failed to fetch transaction' });
  }
});

// Create new transaction
router.post('/', async (req, res) => {
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();

    const {
      id,
      date,
      customer_name,
      member_id,
      outlet_id, // Pastikan outlet_id ada
      subtotal,
      cashier_id, // Add cashier_id to the request body
      tax,
      total,
      amount_paid,
      change_amount,
      payment_method,
      payment_method_id,
      items
    } = req.body;
    let { branch_id } = req.body; // Ambil branch_id dari request body

    // Gunakan date yang dikirim dari client tanpa mengubah format
    // Karena client sudah mengirim dalam format yang benar dengan timezone Asia/Jakarta
    const formattedDate = date || new Date().toISOString().slice(0, 19).replace('T', ' ');

    // Tentukan status pembayaran berdasarkan metode pembayaran
    let payment_status = 'paid'; // Default untuk tunai

    if (payment_method_id) {
      const [paymentMethodResult] = await connection.query(
        'SELECT type FROM payment_methods WHERE id = ?',
        [payment_method_id]
      );

      if (paymentMethodResult.length > 0 && paymentMethodResult[0].type !== 'cash') {
        payment_status = 'pending'; // Non-tunai defaultnya pending
      }
    }

    // Jika branch_id ada, dapatkan outlet_id (user_id admin) dari tabel branches
    let finalOutletId = outlet_id;
    // Jika cashier_id ada, dapatkan branch_id dari tabel cashiers
    if (cashier_id) {
      const [cashierResult] = await connection.query(
        'SELECT branch_id FROM cashiers WHERE id = ?',
        [cashier_id]
      );

      if (cashierResult.length > 0 && cashierResult[0].branch_id) {
        branch_id = cashierResult[0].branch_id;
      }
    }

    if (branch_id && !finalOutletId) {
      // Jika branch_id ada, dapatkan outlet_id (user_id admin) dari tabel branches
      const [branchResult] = await connection.query(
        'SELECT user_id FROM branches WHERE id = ?',
        [branch_id]
      );

      if (branchResult.length > 0 && branchResult[0].user_id) {
        finalOutletId = branchResult[0].user_id;
      }
    }

    // Insert transaction
    const [result] = await connection.query(`
      INSERT INTO transactions (
        id, date, customer_name, member_id, branch_id, outlet_id, subtotal, tax, total, 
        amount_paid, change_amount, payment_method, payment_method_id, payment_status, cashier_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      id, formattedDate, customer_name, member_id || null, branch_id, finalOutletId, subtotal, tax, total,
      amount_paid, change_amount, payment_method, payment_method_id, payment_status, cashier_id
    ]);

    // Insert transaction items
    if (Array.isArray(items) && items.length > 0) {
      for (const item of items) {
        // Ambil cost_price dari tabel products
        let costPrice = 0;
        if (item.product_id) {
          const [productResult] = await connection.query(
            'SELECT cost_price FROM products WHERE id = ?',
            [item.product_id]
          );

          if (productResult.length > 0 && productResult[0].cost_price) {
            costPrice = productResult[0].cost_price;
          }
        }

        await connection.query(`
          INSERT INTO transaction_items (
            transaction_id, product_id, product_name, price, quantity, cost_price
          ) VALUES (?, ?, ?, ?, ?, ?)
        `, [
          id, item.product_id, item.name, item.price, item.quantity, costPrice
        ]);
      }
    }

    await connection.commit();
    res.status(201).json({ id, message: 'Transaction created successfully' });
  } catch (error) {
    await connection.rollback();
    console.error('Error creating transaction:', error);
    res.status(500).json({ error: 'Failed to create transaction' });
  } finally {
    connection.release();
  }
});

// Update payment status
router.patch('/:id/payment-status', async (req, res) => {
  try {
    const { id } = req.params;
    const { payment_status } = req.body;

    // Validasi status
    if (!['paid', 'pending', 'cancelled'].includes(payment_status)) {
      return res.status(400).json({ error: 'Invalid payment status' });
    }

    // Cek status transaksi saat ini
    const [currentStatus] = await pool.query(
      'SELECT payment_status FROM transactions WHERE id = ?',
      [id]
    );

    if (currentStatus.length === 0) {
      return res.status(404).json({ error: 'Transaction not found' });
    }

    // Hanya izinkan perubahan jika status saat ini adalah 'pending'
    if (currentStatus[0].payment_status !== 'pending') {
      return res.status(400).json({ error: 'Only transactions with pending status can be updated' });
    }

    // Update status
    const [result] = await pool.query(
      'UPDATE transactions SET payment_status = ? WHERE id = ?',
      [payment_status, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Transaction not found' });
    }

    res.json({ message: 'Payment status updated successfully' });
  } catch (error) {
    console.error('Error updating payment status:', error);
    res.status(500).json({ error: 'Failed to update payment status' });
  }
});

module.exports = router;




