import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Plus, Edit, Trash, Check, AlertCircle } from 'lucide-react';
import config from '../config';
import { fetchWithSession, getCurrentUser } from '../utils/api';

interface PaymentMethod {
  id: number;
  name: string;
  type: 'cash' | 'bank_transfer' | 'card' | 'qris' | 'e_wallet';
  description: string;
  account_number?: string;
  account_name?: string;
  bank_name?: string;
  qris_image?: string;
  qris_image_url?: string;
  wallet_provider?: string;
  wallet_number?: string;
  is_active: number;
  use_gateway?: number;
}

const PaymentMethodManagement: React.FC = () => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [currentMethod, setCurrentMethod] = useState<PaymentMethod | null>(null);
  const [qrisImage, setQrisImage] = useState<File | null>(null);
  const [qrisPreview, setQrisPreview] = useState<string | null>(null);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [useGateway, setUseGateway] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    type: 'cash',
    description: '',
    account_number: '',
    account_name: '',
    bank_name: '',
    wallet_number: '',
    wallet_provider: '',
    is_active: 1,
    use_gateway: 0
  });

  useEffect(() => {
    fetchPaymentMethods();
  }, []);

  const fetchPaymentMethods = async () => {
    setIsLoading(true);
    try {
      // Dapatkan user_id dari user yang sedang login
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;
      const userRole = currentUser?.role;

      if (!userId) {
        return;
      }

      // Tambahkan user_id sebagai query parameter
      let url = `${config.apiUrl}/payment-methods?user_id=${userId}`;

      // Jika user adalah cabang, tambahkan parameter branch_id
      if (userRole === 'branch') {
        url += `&branch_id=${userId}`;
      }

      const response = await fetchWithSession(url);
      if (response && response.ok) {
        const data = await response.json();
        // Data is already sorted by server
        setPaymentMethods(data);
      }
    } catch (error) { } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;

    if (name === 'use_gateway') {
      setUseGateway(checked);
      // Reset QRIS image if using gateway
      if (checked) {
        setQrisImage(null);
        setQrisPreview(null);
      }
    }

    setFormData(prev => ({
      ...prev,
      [name]: checked ? 1 : 0
    }));
  };

  const handleQrisImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setQrisImage(file);

      // Preview
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target && event.target.result) {
          setQrisPreview(event.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAddMethod = () => {
    setFormData({
      name: '',
      type: 'cash',
      description: '',
      account_number: '',
      account_name: '',
      bank_name: '',
      wallet_number: '',
      wallet_provider: '',
      is_active: 1,
      use_gateway: 0
    });
    setQrisImage(null);
    setQrisPreview(null);
    setUseGateway(false);
    setShowAddModal(true);
  };

  const handleEditMethod = (method: PaymentMethod) => {
    setCurrentMethod(method);
    setFormData({
      name: method.name || '',
      type: method.type || 'cash',
      description: method.description || '',
      account_number: method.account_number || '',
      account_name: method.account_name || '',
      bank_name: method.bank_name || '',
      wallet_number: method.wallet_number || '',
      wallet_provider: method.wallet_provider || '',
      is_active: method.is_active,
      use_gateway: method.use_gateway || 0
    });
    setUseGateway(method.use_gateway === 1);
    setQrisImage(null);
    setQrisPreview(method.qris_image_url || null);
    setShowEditModal(true);
  };

  const handleDeleteMethod = (method: PaymentMethod) => {
    setCurrentMethod(method);
    setShowDeleteModal(true);
  };

  const showSuccess = (message: string) => {
    setAlertMessage(message);
    setShowSuccessAlert(true);
    setTimeout(() => {
      setShowSuccessAlert(false);
    }, 3000);
  };

  const showError = (message: string) => {
    setAlertMessage(message);
    setShowErrorAlert(true);
    setTimeout(() => {
      setShowErrorAlert(false);
    }, 3000);
  };

  const handleSubmitAdd = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Dapatkan user_id dari user yang sedang login
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;

      const formDataObj = new FormData();
      formDataObj.append('name', formData.name);
      formDataObj.append('type', formData.type);
      formDataObj.append('description', formData.description);
      formDataObj.append('is_active', formData.is_active.toString());
      formDataObj.append('user_id', userId.toString());

      // Tambahkan flag use_gateway jika tipe QRIS
      if (formData.type === 'qris') {
        formDataObj.append('use_gateway', formData.use_gateway.toString());
      }

      // Tambahkan data sesuai tipe
      if (formData.type === 'bank_transfer') {
        formDataObj.append('account_number', formData.account_number);
        formDataObj.append('account_name', formData.account_name);
        formDataObj.append('bank_name', formData.bank_name);
      } else if (formData.type === 'qris' && !useGateway && qrisImage) {
        formDataObj.append('qris_image', qrisImage);
      } else if (formData.type === 'e_wallet') {
        formDataObj.append('wallet_provider', formData.wallet_provider);
        formDataObj.append('wallet_number', formData.wallet_number);
      }

      const response = await fetchWithSession(`${config.apiUrl}/payment-methods`, {
        method: 'POST',
        body: formDataObj
      });

      if (response && response.ok) {
        setShowAddModal(false);
        fetchPaymentMethods();
        showSuccess('Metode pembayaran berhasil ditambahkan');
      } else {
        const errorData = await response?.json();
        showError(errorData?.error || 'Gagal menambahkan metode pembayaran');
      }
    } catch (error) {
      showError('Terjadi kesalahan saat menambahkan metode pembayaran');
    }
  };

  const handleSubmitEdit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentMethod) return;

    try {
      // Dapatkan user_id dari user yang sedang login
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;

      const formDataObj = new FormData();
      formDataObj.append('name', formData.name);
      formDataObj.append('type', formData.type);
      formDataObj.append('description', formData.description);
      formDataObj.append('is_active', formData.is_active.toString());
      formDataObj.append('user_id', userId.toString());

      // Tambahkan flag use_gateway jika tipe QRIS
      if (formData.type === 'qris') {
        formDataObj.append('use_gateway', formData.use_gateway.toString());
      }

      // Tambahkan data sesuai tipe
      if (formData.type === 'bank_transfer') {
        formDataObj.append('account_number', formData.account_number);
        formDataObj.append('account_name', formData.account_name);
        formDataObj.append('bank_name', formData.bank_name);
      } else if (formData.type === 'qris' && !useGateway && qrisImage) {
        formDataObj.append('qris_image', qrisImage);
      } else if (formData.type === 'e_wallet') {
        formDataObj.append('wallet_provider', formData.wallet_provider);
        formDataObj.append('wallet_number', formData.wallet_number);
      }

      const response = await fetchWithSession(`${config.apiUrl}/payment-methods/${currentMethod.id}`, {
        method: 'PUT',
        body: formDataObj
      });

      if (response && response.ok) {
        setShowEditModal(false);
        fetchPaymentMethods();
        showSuccess('Metode pembayaran berhasil diperbarui');
      } else {
        const errorData = await response?.json();
        showError(errorData?.error || 'Gagal mengubah metode pembayaran');
      }
    } catch (error) {
      showError('Terjadi kesalahan saat mengubah metode pembayaran');
    }
  };

  const handleConfirmDelete = async () => {
    if (!currentMethod) return;

    try {
      // Dapatkan user_id dari user yang sedang login
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;

      const response = await fetchWithSession(`${config.apiUrl}/payment-methods/${currentMethod.id}?user_id=${userId}`, {
        method: 'DELETE'
      });

      if (response && response.ok) {
        setShowDeleteModal(false);
        fetchPaymentMethods();
        showSuccess('Metode pembayaran berhasil dihapus');
      } else {
        const errorData = await response?.json();
        showError(errorData?.error || 'Gagal menghapus metode pembayaran');
      }
    } catch (error) {
      showError('Terjadi kesalahan saat menghapus metode pembayaran');
    }
  };

  // Render form fields based on payment method type
  const renderTypeSpecificFields = () => {
    switch (formData.type) {
      case 'bank_transfer':
        return (
          <>
            <div className="mb-4">
              <label htmlFor="bank_name" className="block text-sm font-medium text-neutral-700 mb-1">
                Nama Bank <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="bank_name"
                name="bank_name"
                value={formData.bank_name}
                onChange={handleInputChange}
                className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
                autoComplete="off"
              />
            </div>
            <div className="mb-4">
              <label htmlFor="account_number" className="block text-sm font-medium text-neutral-700 mb-1">
                Nomor Rekening <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="account_number"
                name="account_number"
                value={formData.account_number}
                onChange={handleInputChange}
                className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
                autoComplete="off"
              />
            </div>
            <div className="mb-4">
              <label htmlFor="account_name" className="block text-sm font-medium text-neutral-700 mb-1">
                Atas Nama <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="account_name"
                name="account_name"
                value={formData.account_name}
                onChange={handleInputChange}
                className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
                autoComplete="off"
              />
            </div>
          </>
        );
      case 'qris':
        return (
          <>
            <div className="mb-4">
              <div className="flex items-center mb-2">
                <input
                  type="checkbox"
                  id="use_gateway"
                  name="use_gateway"
                  checked={useGateway}
                  onChange={handleCheckboxChange}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded"
                />
                <label htmlFor="use_gateway" className="ml-2 block text-sm font-medium text-neutral-700">
                  Gunakan QRIS dari Payment Gateway
                </label>
              </div>
              <p className="text-xs text-neutral-500 mb-3">
                Jika dicentang, QRIS akan diambil dari payment gateway yang aktif. Jika tidak, Anda perlu mengunggah gambar QRIS.
              </p>

              {!useGateway && (
                <div>
                  <label htmlFor="qris_image" className="block text-sm font-medium text-neutral-700 mb-1">
                    Upload Gambar QRIS <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="file"
                    id="qris_image"
                    name="qris_image"
                    onChange={handleQrisImageChange}
                    className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    accept="image/*"
                    required={formData.type === 'qris' && !useGateway && !qrisPreview}
                  />
                  {qrisPreview && (
                    <div className="mt-2 max-h-48 overflow-y-auto border border-neutral-200 rounded-md">
                      <img src={qrisPreview} alt="QRIS Preview" className="max-w-full h-auto" />
                    </div>
                  )}
                </div>
              )}
            </div>
          </>
        );
      case 'e_wallet':
        return (
          <>
            <div className="mb-4">
              <label htmlFor="wallet_provider" className="block text-sm font-medium text-neutral-700 mb-1">
                Provider E-Wallet <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="wallet_provider"
                name="wallet_provider"
                value={formData.wallet_provider}
                onChange={handleInputChange}
                className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="Contoh: OVO, GoPay, DANA, LinkAja, dll."
                required
                autoComplete="off"
              />
            </div>
            <div className="mb-4">
              <label htmlFor="wallet_number" className="block text-sm font-medium text-neutral-700 mb-1">
                Nomor E-Wallet <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="wallet_number"
                name="wallet_number"
                value={formData.wallet_number}
                onChange={handleInputChange}
                className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="Nomor telepon atau ID akun"
                required
                autoComplete="off"
              />
            </div>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-3">
        <div className="flex items-center gap-3">
          <Link to="/" className="p-2 rounded-full hover:bg-neutral-100">
            <ArrowLeft size={20} />
          </Link>
          <h1 className="text-2xl font-bold text-primary-800">Manajemen Metode Pembayaran</h1>
        </div>
        <button
          onClick={handleAddMethod}
          className="flex items-center justify-center gap-2 bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 w-full sm:w-auto"
        >
          <Plus size={18} />
          <span>Tambah Metode Pembayaran</span>
        </button>
      </div>

      {/* Success Alert */}
      {showSuccessAlert && (
        <div className="bg-green-100 border border-green-200 text-green-800 rounded-lg p-4 mb-6 flex items-center">
          <Check size={20} className="mr-2" />
          <span>{alertMessage}</span>
        </div>
      )}

      {/* Error Alert */}
      {showErrorAlert && (
        <div className="bg-red-100 border border-red-200 text-red-800 rounded-lg p-4 mb-6 flex items-center">
          <AlertCircle size={20} className="mr-2" />
          <span>{alertMessage}</span>
        </div>
      )}

      {/* Payment Methods Table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-neutral-200">
            <thead className="bg-neutral-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                  Nama
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                  Tipe
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                  Detail
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider">
                  Aksi
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-neutral-200">
              {isLoading ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-neutral-500">
                    Memuat data...
                  </td>
                </tr>
              ) : paymentMethods.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-neutral-500">
                    Belum ada metode pembayaran
                  </td>
                </tr>
              ) : (
                paymentMethods.map((method) => (
                  <tr key={method.id} className="hover:bg-neutral-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-neutral-900">
                      {method.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                      {method.type === 'cash' && 'Tunai'}
                      {method.type === 'bank_transfer' && 'Transfer Bank'}
                      {method.type === 'qris' && 'QRIS'}
                      {method.type === 'card' && 'Kartu Kredit/Debit'}
                      {method.type === 'e_wallet' && 'E-Money / E-Wallet'}
                    </td>
                    <td className="px-6 py-4 text-sm text-neutral-500">
                      {method.type === 'bank_transfer' && (
                        <div>
                          <p>{method.bank_name}</p>
                          <p>{method.account_number}</p>
                          <p className="text-xs">{method.account_name}</p>
                        </div>
                      )}
                      {method.type === 'qris' && (
                        <div>
                          {method.use_gateway === 1 ? (
                            <span className="text-primary-600">Menggunakan Payment Gateway</span>
                          ) : method.qris_image ? (
                            <a
                              href={method.qris_image_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-primary-600 hover:underline"
                            >
                              Lihat Gambar QRIS
                            </a>
                          ) : (
                            <span className="text-neutral-400">Tidak ada gambar</span>
                          )}
                        </div>
                      )}
                      {method.type === 'e_wallet' && (
                        <div>
                          <p>{method.wallet_provider}</p>
                          <p className="text-xs">{method.wallet_number}</p>
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                      {method.is_active === 1 ? (
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Aktif
                        </span>
                      ) : (
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-neutral-100 text-neutral-800">
                          Tidak Aktif
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end gap-2">
                        <button
                          onClick={() => handleEditMethod(method)}
                          className="text-primary-600 hover:text-primary-900 focus:outline-none focus:underline"
                        >
                          <Edit size={18} />
                        </button>
                        <button
                          onClick={() => handleDeleteMethod(method)}
                          className="text-red-600 hover:text-red-900 focus:outline-none focus:underline"
                        >
                          <Trash size={18} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-md p-6 animate-fade-in max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-medium text-neutral-900 mb-4">Tambah Metode Pembayaran</h3>
            <form onSubmit={handleSubmitAdd} autoComplete="off">
              <div className="mb-4">
                <label htmlFor="name" className="block text-sm font-medium text-neutral-700 mb-1">
                  Nama Metode Pembayaran <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                  autoComplete="off"
                />
              </div>
              <div className="mb-4">
                <label htmlFor="type" className="block text-sm font-medium text-neutral-700 mb-1">
                  Tipe Metode Pembayaran <span className="text-red-500">*</span>
                </label>
                <select
                  id="type"
                  name="type"
                  value={formData.type}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  autoComplete="off"
                >
                  <option value="cash">Tunai</option>
                  <option value="bank_transfer">Transfer Bank</option>
                  <option value="qris">QRIS</option>
                  <option value="card">Kartu Kredit/Debit</option>
                  <option value="e_wallet">E-Money / E-Wallet</option>
                </select>
              </div>
              {renderTypeSpecificFields()}
              <div className="mb-4">
                <label htmlFor="description" className="block text-sm font-medium text-neutral-700 mb-1">
                  Deskripsi (Opsional)
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  rows={3}
                  autoComplete="off"
                />
              </div>
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_active"
                    checked={formData.is_active === 1}
                    onChange={(e) => setFormData({ ...formData, is_active: e.target.checked ? 1 : 0 })}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded"
                  />
                  <span className="ml-2 text-sm text-neutral-700">Aktif</span>
                </label>
              </div>
              <div className="flex justify-end gap-2 mt-6">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="px-4 py-2 border border-neutral-300 rounded-md text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  Batal
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  Simpan
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {showEditModal && currentMethod && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-md p-6 animate-fade-in max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-medium text-neutral-900 mb-4">Edit Metode Pembayaran</h3>
            <form onSubmit={handleSubmitEdit} autoComplete="off">
              <div className="mb-4">
                <label htmlFor="edit_name" className="block text-sm font-medium text-neutral-700 mb-1">
                  Nama Metode Pembayaran <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="edit_name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                  autoComplete="off"
                />
              </div>
              <div className="mb-4">
                <label htmlFor="edit_type" className="block text-sm font-medium text-neutral-700 mb-1">
                  Tipe Metode Pembayaran <span className="text-red-500">*</span>
                </label>
                <select
                  id="edit_type"
                  name="type"
                  value={formData.type}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  autoComplete="off"
                >
                  <option value="cash">Tunai</option>
                  <option value="bank_transfer">Transfer Bank</option>
                  <option value="qris">QRIS</option>
                  <option value="card">Kartu Kredit/Debit</option>
                  <option value="e_wallet">E-Money / E-Wallet</option>
                </select>
              </div>
              {renderTypeSpecificFields()}
              <div className="mb-4">
                <label htmlFor="edit_description" className="block text-sm font-medium text-neutral-700 mb-1">
                  Deskripsi (Opsional)
                </label>
                <textarea
                  id="edit_description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  rows={3}
                  autoComplete="off"
                />
              </div>
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_active"
                    checked={formData.is_active === 1}
                    onChange={(e) => setFormData({ ...formData, is_active: e.target.checked ? 1 : 0 })}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded"
                  />
                  <span className="ml-2 text-sm text-neutral-700">Aktif</span>
                </label>
              </div>
              <div className="flex justify-end gap-2 mt-6">
                <button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  className="px-4 py-2 border border-neutral-300 rounded-md text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  Batal
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  Simpan
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && currentMethod && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-md p-6 animate-fade-in">
            <h3 className="text-lg font-medium text-neutral-900 mb-2">Konfirmasi Hapus</h3>
            <p className="text-neutral-600 mb-4">
              Apakah Anda yakin ingin menghapus metode pembayaran "{currentMethod.name}"?
            </p>
            <div className="flex justify-end gap-2">
              <button
                onClick={() => setShowDeleteModal(false)}
                className="px-4 py-2 border border-neutral-300 rounded-md text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                Batal
              </button>
              <button
                onClick={handleConfirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                Hapus
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentMethodManagement;











