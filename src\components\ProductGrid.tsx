import React, { useState, useEffect } from 'react';
import { useCart } from '../context/CartContext';
import config from '../config';
import { fetchWithSession, getCurrentUser, buildApiUrl } from '../utils/api';
import { MenuItem, Product } from '../types';

interface ProductGridProps {
  category: string;
  searchQuery: string;
}

const ProductGrid: React.FC<ProductGridProps> = ({ category, searchQuery }) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { addToCart } = useCart();

  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);
      try {
        // Dapatkan user_id dari user yang sedang login
        const currentUser = getCurrentUser();
        const userRole = currentUser?.role;

        if (!currentUser) {
          return;
        }

        // Build URL dengan parameter yang benar untuk kasir
        let additionalParams: Record<string, string | number> = {};

        // Jika user adalah cabang, tambahkan parameter branch_id
        if (userRole === 'branch') {
          additionalParams.branch_id = currentUser.id;
        }

        const url = buildApiUrl(`${config.apiUrl}/products`, additionalParams);
        const response = await fetchWithSession(url);
        if (response && response.ok) {
          const data = await response.json();
          setProducts(data);
        }
      } catch (error) { } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  const filteredProducts = products.filter(product => {
    const matchesCategory = category === 'all' || product.category === category;
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const handleProductClick = (product: Product) => {
    // Konversi Product ke MenuItem untuk ditambahkan ke cart
    const menuItem: MenuItem = {
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      category: product.category,
      description: ''
    };

    addToCart(menuItem);
  };

  // Fungsi untuk mendapatkan URL gambar yang benar
  const getImageUrl = (imagePath: string) => {
    if (!imagePath) {
      return 'https://via.placeholder.com/150?text=No+Image';
    }

    if (imagePath.startsWith('/uploads/')) {
      return `${config.imageBaseUrl}${imagePath}`;
    }

    // For URL images, return as-is
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return imagePath;
    }

    // For relative paths, prepend imageBaseUrl
    return `${config.imageBaseUrl}${imagePath}`;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary-500 border-t-transparent"></div>
      </div>
    );
  }

  if (filteredProducts.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-neutral-500">Tidak ada produk yang ditemukan</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 md:gap-4">
      {filteredProducts.map(product => (
        <div
          key={product.id}
          className="bg-white rounded-lg shadow-sm overflow-hidden cursor-pointer hover:shadow-md transition-shadow product-card"
          onClick={() => handleProductClick(product)}
        >
          <div className="h-36 md:h-40 overflow-hidden product-card-image">
            <img
              src={getImageUrl(product.image)}
              alt={product.name}
              className="w-full h-full object-cover"
              onError={(e) => {
                e.currentTarget.src = 'https://via.placeholder.com/150?text=Error';
              }}
            />
          </div>
          <div className="p-3 md:p-4 flex-1 flex flex-col">
            <h3 className="font-medium text-neutral-800 text-sm md:text-base line-clamp-2">{product.name}</h3>
            <div className="flex justify-between items-center mt-auto pt-2">
              <span className="text-primary-600 font-bold text-sm md:text-base">{formatPrice(product.price)}</span>
              <span className="text-xs px-2 py-1 bg-neutral-100 rounded-full text-neutral-600 category-label">
                {product.categoryLabel}
              </span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProductGrid;




