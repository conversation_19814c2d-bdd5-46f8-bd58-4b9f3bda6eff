import React, { useState, useEffect, useRef } from 'react';
import { useCart } from '../context/CartContext';
import { Pencil, X, CheckCircle, CreditCard, Banknote, Plus, Search, Users, Printer, Bluetooth, FileText } from 'lucide-react';
import config from '../config';
import { fetchWithSession, getCurrentUser } from '../utils/api';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import BluetoothPrinter from '../utils/BluetoothPrinter';

// Add this interface with your other interfaces
interface StoreConfig {
  store_name: string;
  // Add other properties as needed
}

// Tambahkan interface Member
interface Member {
  id: number;
  name: string;
  phone: string;
  email: string | null;
}

const OrderPanel: React.FC = () => {
  const {
    cartItems,
    removeFromCart,
    updateQuantity,
    clearCart,
    updatePaymentAmount,
    customerName,
    updateCustomerName,
    memberId, // Gunakan dari context
    updateMemberId // Gunakan dari context
  } = useCart();

  // Add this with your other state variables
  const [storeConfig, setStoreConfig] = useState<StoreConfig>({ store_name: 'Bakery POS' });
  const [taxPercentage, setTaxPercentage] = useState<number>(0);

  const printRef = useRef<HTMLDivElement>(null);

  // Gunakan state lokal untuk menyimpan member_id
  const [orderNumber, setOrderNumber] = useState('#001');
  const [showEditPopup, setShowEditPopup] = useState(false);
  const [tempCustomerName, setTempCustomerName] = useState('');
  const [paymentAmount, setPaymentAmount] = useState('');
  const [change, setChange] = useState(0);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  // State untuk member
  const [showMemberModal, setShowMemberModal] = useState(false);
  const [members, setMembers] = useState<Member[]>([]);
  const [filteredMembers, setFilteredMembers] = useState<Member[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoadingMembers, setIsLoadingMembers] = useState(false);
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);

  // State untuk tambah member baru
  const [showAddMemberForm, setShowAddMemberForm] = useState(false);
  const [newMemberData, setNewMemberData] = useState({
    name: '',
    phone: '',
    email: '',
  });
  const [isSavingMember, setIsSavingMember] = useState(false);

  // Fungsi untuk membuka modal member
  const openMemberModal = () => {
    setShowMemberModal(true);
    setSearchQuery('');
    fetchMembers();
  };

  // Fungsi untuk menutup modal member
  const closeMemberModal = () => {
    setShowMemberModal(false);
    setSearchQuery('');
  };

  // Fungsi untuk fetch data member
  const fetchMembers = async () => {
    try {
      setIsLoadingMembers(true);

      // Dapatkan user_id dari user yang sedang login
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;
      const userRole = currentUser?.role;

      if (!userId) {
        return;
      }

      // Tambahkan user_id sebagai query parameter
      let url = `${config.apiUrl}/members?user_id=${userId}`;

      // Jika user adalah cabang, tambahkan parameter branch_id
      if (userRole === 'branch') {
        url += `&branch_id=${userId}`;
      } else if (userRole == 'cashier') {
        // Jika user adalah kasir, tambahkan parameter cashier_id
        url += `&cashier_id=${userId}`;
      }

      const response = await fetchWithSession(url);
      if (response && response.ok) {
        const data = await response.json();
        setMembers(data);
      }
    } catch (error) { } finally {
      setIsLoadingMembers(false);
    }
  };

  // Filter members berdasarkan search query
  useEffect(() => {
    if (members.length > 0) {
      const filtered = members.filter(member =>
        member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        member.phone.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (member.email && member.email.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredMembers(filtered);
    }
  }, [searchQuery, members]);

  // Fungsi untuk memilih member
  const selectMember = (member: Member) => {
    setSelectedMember(member);
    setTempCustomerName(member.name);
    updateMemberId(member.id); // Gunakan fungsi dari context
    closeMemberModal();
  };

  // Fungsi untuk menangani perubahan pada form tambah member
  const handleNewMemberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewMemberData(prev => ({ ...prev, [name]: value }));
  };

  // Fungsi untuk menyimpan member baru
  const handleSaveMember = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMemberData.name || !newMemberData.phone) {
      alert('Nama dan nomor telepon harus diisi');
      return;
    }

    setIsSavingMember(true);

    try {
      // Dapatkan user_id dari user yang sedang login
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;
      const userRole = currentUser?.role;

      if (!userId) {
        return;
      }

      const memberData = {
        name: newMemberData.name,
        phone: newMemberData.phone,
        email: newMemberData.email || null,
        is_active: 1,
        user_id: userId,
        branch_id: userRole === 'branch' ? userId : null,
        cashier_id: userRole === 'cashier' ? userId : null
      };

      const response = await fetchWithSession(`${config.apiUrl}/members`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(memberData)
      });

      if (response && response.ok) {
        const savedMember = await response.json();

        // Tambahkan member baru ke state
        setMembers(prev => [savedMember, ...prev]);

        // Reset form
        setNewMemberData({
          name: '',
          phone: '',
          email: ''
        });

        // Tutup form tambah member
        setShowAddMemberForm(false);

        // Pilih member yang baru ditambahkan
        selectMember(savedMember);
      } else {
        const errorData = await response?.json();
        alert(errorData?.error || 'Gagal menambahkan member');
      }
    } catch (error) {
      alert('Terjadi kesalahan saat menyimpan member');
    } finally {
      setIsSavingMember(false);
    }
  };

  // Perbarui tipe data untuk successData
  interface SuccessData {
    total: number;
    payment: number;
    change: number;
    paymentMethod?: string;
    isNonCash?: boolean;
    isGateway?: boolean;
    gatewayData?: {
      qr_code_url: string;
      transaction_id: string;
      expiry_time: string;
      transaction_status?: string;
    };
  }

  interface TransactionItem {
    id?: number;
    transaction_id: string;
    product_id: number; // Tambahkan product_id
    product_name: string;
    price: number;
    quantity: number;
  }

  interface Transaction {
    id: string;
    date: string;
    customer_name: string;
    branch_id: number | null;
    branch_name: string | null;
    subtotal: number;
    tax: number;
    total: number;
    amount_paid: number;
    change_amount: number;
    payment_method: string;
    payment_method_id: number | null;
    payment_method_name?: string;
    payment_method_type?: string;
    payment_status: 'paid' | 'pending' | 'cancelled'; // Tambahkan status pembayaran
    items: TransactionItem[];
    cashier_name?: string; // Add cashier_name to the Transaction interface
    // Detail metode pembayaran
    bank_name?: string | null;
    account_number?: string | null;
    account_name?: string | null;
    qris_image?: string | null;
    qris_image_url?: string | null;
    wallet_provider?: string | null;
    wallet_number?: string | null;
    // Gateway info untuk QR code dari Midtrans
    gateway_info?: {
      gateway_transaction_id: string;
      gateway_provider: string;
      payment_type: string;
      status: string;
      qr_code_url?: string;
      expiry_time?: string;
      transaction_status?: string;
    } | null;
  }

  // Perbarui state successData dengan tipe yang baru
  const [successData, setSuccessData] = useState<SuccessData>({
    total: 0,
    payment: 0,
    change: 0
  });

  // State baru untuk modal pembayaran
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [activeTab, setActiveTab] = useState('cash'); // 'cash' atau 'non-cash'
  const [customAmount, setCustomAmount] = useState(false);
  const [customAmountValue, setCustomAmountValue] = useState('');

  // State untuk menyimpan daftar metode pembayaran
  const [paymentMethods, setPaymentMethods] = useState<any[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<any>(null);

  // Tambahkan state untuk menyimpan ID transaksi yang baru dibuat
  const [createdTransactionId, setCreatedTransactionId] = useState<string>('');

  // Tambahkan state untuk mengetahui apakah metode pembayaran menggunakan gateway
  const [isUsingGateway, setIsUsingGateway] = useState<boolean>(false);

  // Tambahkan state untuk menunjukkan proses update status
  const [isUpdatingStatus, setIsUpdatingStatus] = useState<boolean>(false);

  // Tambahkan state untuk modal status update
  const [showStatusUpdateModal, setShowStatusUpdateModal] = useState(false);
  const [statusUpdateMessage, setStatusUpdateMessage] = useState('');
  const [statusUpdateType, setStatusUpdateType] = useState<'success' | 'error'>('success');

  // Tambahkan state untuk menyimpan data QRIS dari payment gateway
  const [gatewayQrisData, setGatewayQrisData] = useState<{
    qr_code_url: string;
    transaction_id: string;
    expiry_time: string;
  } | null>(null);

  // Tambahkan state untuk menunjukkan proses loading gateway
  const [isLoadingGateway, setIsLoadingGateway] = useState(false);

  // State untuk polling status transaksi
  const [isPolling, setIsPolling] = useState(false);
  const [pollingInterval, setPollingInterval] = useState<number | null>(null);
  const [transactionStatus, setTransactionStatus] = useState<string>('pending');

  // Tambahkan state untuk modal detail transaksi
  const [showTransactionDetailModal, setShowTransactionDetailModal] = useState(false);
  const [transactionDetail, setTransactionDetail] = useState<Transaction | null>(null);

  // Add these state variables to your component
  const [showPrintOptions, setShowPrintOptions] = useState(false);
  const [isPrinting, setIsPrinting] = useState(false);

  // Fungsi untuk menampilkan detail transaksi
  const handleViewTransactionDetail = async () => {
    if (!createdTransactionId) return;

    try {
      const response = await fetchWithSession(`${config.apiUrl}/transactions/${createdTransactionId}`);
      if (response && response.ok) {
        const detailedTransaction = await response.json();

        // Pastikan items selalu ada, jika tidak ada, berikan array kosong
        if (!detailedTransaction.items || !Array.isArray(detailedTransaction.items)) {
          detailedTransaction.items = [];
        }

        setTransactionDetail(detailedTransaction);
        setShowTransactionDetailModal(true);
      } else {
        alert('Gagal mengambil detail transaksi');
      }
    } catch (error) {
      alert('Gagal mengambil detail transaksi');
    }
  };

  // Fungsi untuk mendapatkan label status
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'paid':
        return 'Lunas';
      case 'pending':
        return 'Menunggu';
      case 'cancelled':
        return 'Dibatalkan';
      default:
        return 'Unknown';
    }
  };

  // Fungsi untuk mendapatkan label metode pembayaran
  const getPaymentMethodLabel = (transaction: any) => {
    if (transaction.payment_method_name) {
      return transaction.payment_method_name;
    }
    return transaction.payment_method || 'Tunai';
  };

  // Fungsi untuk mendapatkan informasi metode pembayaran
  const getPaymentMethodInfo = (transaction: any) => {
    if (transaction.payment_method_type === 'cash') {
      return (
        <div>
          <p><span className="font-medium">Dibayar:</span> {formatPrice(transaction.amount_paid || 0)}</p>
          <p><span className="font-medium">Kembalian:</span> {formatPrice(transaction.change_amount || 0)}</p>
        </div>
      );
    }

    return null;
  };

  // Fungsi untuk mengambil konfigurasi pajak dari store
  const fetchTaxConfiguration = async () => {
    try {
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;

      if (!userId) return;

      const response = await fetchWithSession(`${config.apiUrl}/store-config?user_id=${userId}`);
      if (response && response.ok) {
        const data = await response.json();
        setTaxPercentage(data.tax_percentage || 0);
        setStoreConfig(data);
      }
    } catch (error) {
      console.error('Error fetching tax configuration:', error);
      setTaxPercentage(0);
    }
  };

  // Generate order number when component mounts
  useEffect(() => {
    generateOrderNumber();
    fetchTaxConfiguration();
  }, []);

  // Cleanup polling saat component unmount
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [pollingInterval]);

  // Fungsi untuk menghasilkan nomor order otomatis dengan timezone Asia/Jakarta
  const generateOrderNumber = () => {
    // Gunakan timestamp dengan timezone Asia/Jakarta
    const now = new Date();
    const jakartaTime = new Date(now.toLocaleString('en-US', { timeZone: 'Asia/Jakarta' }));
    const timestamp = jakartaTime.getTime();

    const newOrderNumber = `TRX-${timestamp}`;
    setOrderNumber(newOrderNumber);
    return newOrderNumber;
  };

  const subtotal = cartItems.reduce((sum, item) => {
    // Periksa apakah item.menuItem ada dan memiliki properti price
    const price = item.menuItem ? item.menuItem.price : (item.price || 0);
    const quantity = item.quantity || 1;
    return sum + (price * quantity);
  }, 0);

  // Hitung pajak berdasarkan persentase dari store config
  const tax = subtotal * (taxPercentage / 100);
  const total = subtotal + tax;

  useEffect(() => {
    // Hitung kembalian saat jumlah pembayaran berubah
    const amount = paymentAmount ? parseInt(paymentAmount) : 0;
    const changeAmount = amount - total;
    setChange(changeAmount > 0 ? changeAmount : 0);
  }, [paymentAmount, total]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  // Fungsi baru untuk menangani pembayaran
  const handlePaymentButtonClick = (amount: number) => {
    const amountStr = amount.toString();
    setPaymentAmount(amountStr);
    updatePaymentAmount(amount);
    setCustomAmount(false);
    setCustomAmountValue('');
  };

  const handleCustomAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '');
    setCustomAmountValue(value);
    setPaymentAmount(value);
    updatePaymentAmount(value ? parseInt(value) : 0);
  };

  // Modifikasi handleCheckout untuk menampilkan modal pembayaran
  const handleCheckout = () => {
    // Validasi
    if (cartItems.length === 0) {
      alert('Keranjang kosong');
      return;
    }

    setShowPaymentModal(true);
    setActiveTab('cash');
    setCustomAmount(false);
    setCustomAmountValue('');
    setPaymentAmount('');

    // Pastikan memberId tidak direset saat membuka modal pembayaran
  };

  // Fungsi untuk melakukan pembayaran setelah konfirmasi
  const handleConfirmPayment = async () => {
    if (activeTab === 'cash' && (!paymentAmount || parseFloat(paymentAmount) < total)) {
      alert('Pembayaran kurang dari total');
      return;
    }

    if (activeTab === 'non-cash' && !selectedPaymentMethod) {
      alert('Pilih metode pembayaran terlebih dahulu');
      return;
    }

    const paymentAmountNum = activeTab === 'cash' ? parseFloat(paymentAmount) : total;
    const change = activeTab === 'cash' ? paymentAmountNum - total : 0;
    const paymentMethodName = activeTab === 'cash' ? 'Cash' : selectedPaymentMethod?.name || 'Non-Cash';

    // Cek apakah metode pembayaran menggunakan gateway
    const useGateway = selectedPaymentMethod?.use_gateway === 1;
    setIsUsingGateway(useGateway);

    // Buat objek tanggal dengan timezone Asia/Jakarta
    const now = new Date();
    const jakartaTime = new Date(now.toLocaleString('en-US', { timeZone: 'Asia/Jakarta' }));

    // Format tanggal ke format MySQL (YYYY-MM-DD HH:MM:SS)
    const year = jakartaTime.getFullYear();
    const month = String(jakartaTime.getMonth() + 1).padStart(2, '0');
    const day = String(jakartaTime.getDate()).padStart(2, '0');
    const hours = String(jakartaTime.getHours()).padStart(2, '0');
    const minutes = String(jakartaTime.getMinutes()).padStart(2, '0');
    const seconds = String(jakartaTime.getSeconds()).padStart(2, '0');

    const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

    // Dapatkan branch_id dan outlet_id dari user yang sedang login
    const currentUser = getCurrentUser();
    const branchId = currentUser?.role === 'branch' ? currentUser?.id : null;
    const outletId = currentUser?.role === 'admin' ? currentUser?.id : null;

    const transaction = {
      id: orderNumber,
      date: formattedDate,
      customer_name: customerName,
      member_id: memberId, // Gunakan memberId lokal
      branch_id: branchId,
      outlet_id: outletId, // Tambahkan outlet_id
      subtotal: subtotal,
      tax: tax,
      total: total,
      amount_paid: paymentAmountNum,
      change_amount: change,
      payment_method: paymentMethodName,
      payment_method_id: activeTab === 'cash' ? null : selectedPaymentMethod?.id,
      payment_method_type: activeTab === 'cash' ? 'cash' : selectedPaymentMethod?.type || 'non-cash',
      items: cartItems.map(item => ({
        product_id: item.menuItem?.id,
        name: item.menuItem?.name,
        price: item.menuItem?.price,
        quantity: item.quantity
      }))
    };

    try {
      // Kirim transaksi ke API
      const response = await fetch(`${config.apiUrl}/transactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...transaction,
          cashier_id: currentUser?.role === 'cashier' ? currentUser?.id : null // Add cashier_id if the user is a cashier
        })
      });

      if (!response.ok) {
        alert('Gagal menyimpan transaksi ke database');
        return;
      }

      // Ambil data transaksi dari response
      const transactionData = await response.json();

      // Simpan ID transaksi yang baru dibuat
      setCreatedTransactionId(transactionData.id);

      // Jika menggunakan payment gateway, proses pembayaran melalui gateway
      if (useGateway && activeTab === 'non-cash') {
        try {
          const gatewayData = await processPaymentGateway({
            id: transactionData.id,
            total: total
          });

          // Set success data dengan informasi gateway
          setSuccessData({
            total: total,
            payment: total,
            change: 0,
            paymentMethod: paymentMethodName,
            isNonCash: true,
            isGateway: true,
            gatewayData: {
              ...gatewayData,
              transaction_status: 'pending'
            }
          });

          // Tutup modal pembayaran dan tampilkan modal sukses dengan QR code
          setShowPaymentModal(false);
          setShowSuccessModal(true);

          // Mulai polling status transaksi menggunakan gateway transaction ID
          if (gatewayData.transaction_id) {
            startPolling(gatewayData.transaction_id);
          }
        } catch (error: any) {
          console.error('Payment gateway error:', error);
          let errorMessage = 'Gagal memproses pembayaran melalui gateway';

          if (error.message) {
            errorMessage = error.message;
          }

          alert(errorMessage);
          return;
        }
      } else {
        // Jika tidak menggunakan payment gateway, tampilkan modal sukses biasa
        setSuccessData({
          total: total,
          payment: paymentAmountNum,
          change: change,
          paymentMethod: paymentMethodName,
          isNonCash: activeTab === 'non-cash'
        });

        // Tutup modal pembayaran
        setShowPaymentModal(false);

        // Tampilkan modal sukses
        setShowSuccessModal(true);
      }

      // Bersihkan cart
      clearCart();
      setPaymentAmount('');
      setChange(0);

      // Reset selectedMember
      updateMemberId(null);

      // Generate nomor order baru untuk transaksi berikutnya
      generateOrderNumber();
    } catch (error) {
      alert('Gagal menyimpan transaksi ke database');
    }
  };

  // Fungsi untuk menutup modal sukses
  const closeSuccessModal = () => {
    setShowSuccessModal(false);
    // Stop polling saat modal ditutup
    stopPolling();
  };

  // Fungsi untuk memulai polling status transaksi dari Midtrans
  const startPolling = (gatewayTransactionId: string) => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
    }

    setIsPolling(true);
    setTransactionStatus('pending');

    const interval = setInterval(async () => {
      try {
        // Gunakan API endpoint kita yang akan memanggil Midtrans API
        const response = await fetchWithSession(`${config.apiUrl}/payment-gateway/status/${gatewayTransactionId}`);
        if (response && response.ok) {
          const statusData = await response.json();
          const status = statusData.transaction_status;

          setTransactionStatus(status);

          // Update successData dengan status terbaru
          setSuccessData(prev => ({
            ...prev,
            gatewayData: prev.gatewayData ? {
              ...prev.gatewayData,
              transaction_status: status
            } : undefined
          }));

          // Jika status sudah settlement/capture (paid) atau failure/cancel/expire, stop polling
          if (status === 'settlement' || status === 'capture' ||
            status === 'failure' || status === 'cancel' || status === 'expire') {
            stopPolling();

            // Tampilkan notifikasi
            if (status === 'settlement' || status === 'capture') {
              alert('Pembayaran berhasil! Transaksi telah lunas.');

              // Update status transaksi di database kita
              await updateTransactionStatus(createdTransactionId, 'paid');
            } else if (status === 'failure' || status === 'cancel' || status === 'expire') {
              alert('Pembayaran dibatalkan atau gagal.');

              // Update status transaksi di database kita
              await updateTransactionStatus(createdTransactionId, 'cancelled');
            }
          }
        }
      } catch (error) {
        console.error('Error polling transaction status:', error);
      }
    }, 5000); // Poll setiap 5 detik

    setPollingInterval(interval);
  };

  // Fungsi untuk update status transaksi di database kita
  const updateTransactionStatus = async (transactionId: string, status: 'paid' | 'cancelled') => {
    try {
      await fetchWithSession(`${config.apiUrl}/transactions/${transactionId}/payment-status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ payment_status: status })
      });
    } catch (error) {
      console.error('Error updating transaction status:', error);
    }
  };

  // Fungsi untuk menghentikan polling
  const stopPolling = () => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }
    setIsPolling(false);
  };

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      removeFromCart(itemId);
    } else {
      updateQuantity(itemId, newQuantity);
    }
  };

  const openEditPopup = () => {
    setTempCustomerName(customerName);

    // Jika ada memberId, cari member yang sesuai
    if (memberId) {
      const member = members.find(m => m.id === memberId);
      if (member) {
        setSelectedMember(member);
      }
    }

    setShowEditPopup(true);
  };

  const saveCustomerName = () => {
    updateCustomerName(tempCustomerName);
    // Jika tidak memilih dari member, reset member_id
    if (!selectedMember) {
      updateMemberId(null); // Gunakan fungsi dari context
    }
    setShowEditPopup(false);
  };

  // Tambahkan useEffect untuk mengambil daftar metode pembayaran
  useEffect(() => {
    const fetchPaymentMethods = async () => {
      try {
        // Dapatkan user_id dari user yang sedang login
        const currentUser = getCurrentUser();
        const userId = currentUser?.id;
        const userRole = currentUser?.role;

        if (!userId) {
          return;
        }

        // Tambahkan user_id sebagai query parameter
        let url = `${config.apiUrl}/payment-methods?user_id=${userId}`;

        // Jika user adalah cabang, tambahkan parameter branch_id
        if (userRole === 'branch') {
          url += `&branch_id=${userId}`;
        }

        // Jika user adalah kasir, tambahkan parameter cashier_id
        if (userRole === 'cashier') {
          url += `&cashier_id=${userId}`;
        }

        const response = await fetchWithSession(url);
        if (response && response.ok) {
          const data = await response.json();
          setPaymentMethods(data);
        }
      } catch (error) { }
    };

    fetchPaymentMethods();
  }, []);

  // Fungsi untuk memproses pembayaran melalui payment gateway
  const processPaymentGateway = async (transactionData: { id: string, total: number }) => {
    try {
      setIsLoadingGateway(true);

      // Dapatkan user_id dari user yang sedang login
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;
      const userRole = currentUser?.role;

      if (!userId) {
        throw new Error('User ID not found');
      }

      // Tambahkan user_id sebagai query parameter
      let url = `${config.apiUrl}/payment-gateway/process`;

      // Siapkan data untuk dikirim ke server
      const requestData = {
        transaction_id: transactionData.id,
        amount: transactionData.total,
        payment_method_id: selectedPaymentMethod.id,
        payment_type: selectedPaymentMethod.type,
        user_id: userId,
        branch_id: userRole === 'branch' ? userId : null,
        cashier_id: userRole === 'cashier' ? userId : null
      };

      const response = await fetchWithSession(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      if (response && response.ok) {
        const data = await response.json();
        setGatewayQrisData(data);
        return data;
      } else {
        const errorData = await response?.json();
        let errorMessage = 'Gagal memproses pembayaran melalui gateway';

        if (errorData?.error) {
          errorMessage = errorData.error;
        }

        if (errorData?.message) {
          errorMessage += ': ' + errorData.message;
        }

        if (errorData?.details) {
          console.error('Payment gateway details:', errorData.details);
        }

        throw new Error(errorMessage);
      }
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error('Terjadi kesalahan yang tidak diketahui saat memproses pembayaran');
      }
    } finally {
      setIsLoadingGateway(false);
    }
  };

  // Add this to your useEffect section
  useEffect(() => {
    fetchStoreConfig();
  }, []);

  // Add this function to your component
  const fetchStoreConfig = async () => {
    try {
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;

      if (!userId) return;

      const response = await fetchWithSession(`${config.apiUrl}/store-config?user_id=${userId}`);
      if (response && response.ok) {
        const data = await response.json();
        setStoreConfig(data);
      }
    } catch (error) {
      // Handle error silently
    }
  };

  // Fungsi untuk menampilkan opsi cetak
  const handlePrintOptions = () => {
    setShowPrintOptions(true);
  };

  // Fungsi untuk mencetak ke printer Bluetooth
  const printToBluetoothPrinter = async () => {
    if (!transactionDetail) return;
    setShowPrintOptions(false);

    try {
      setIsPrinting(true);

      // Add store name to transaction for receipt
      const transactionWithStoreName = {
        ...transactionDetail,
        store_name: storeConfig?.store_name || 'Bakery POS'
      };

      // Gunakan method printWithFallback yang mencoba beberapa format
      const success = await BluetoothPrinter.printWithFallback(transactionWithStoreName);

      if (success) {
        alert('Struk berhasil dicetak');
      } else {
        alert('Gagal mencetak struk. Pastikan printer terhubung dengan benar dan dalam keadaan siap. Periksa console untuk detail error.');
      }
    } catch (error) {
      alert('Gagal mencetak: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsPrinting(false);
    }
  };

  // Fungsi untuk mencetak ke PDF
  const printToPDF = () => {
    if (!transactionDetail) return;
    setShowPrintOptions(false);

    try {
      setIsPrinting(true);

      const doc = new jsPDF();

      // Header
      doc.setFontSize(18);
      doc.text(storeConfig?.store_name || 'Bakery POS', 105, 15, { align: 'center' });
      doc.setFontSize(12);
      doc.text(transactionDetail.branch_name || 'Cabang Utama', 105, 22, { align: 'center' });
      doc.text(new Date(transactionDetail.date).toLocaleString('id-ID', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }), 105, 28, { align: 'center' });

      // Transaction info
      doc.setFontSize(10);
      doc.text(`ID Transaksi: ${transactionDetail.id}`, 14, 40);
      doc.text(`Pelanggan: ${transactionDetail.customer_name}`, 14, 46);
      doc.text(`Metode Pembayaran: ${getPaymentMethodLabel(transactionDetail)}`, 14, 52);

      // Status pembayaran
      const statusLabel = getStatusLabel(transactionDetail.payment_status);
      doc.text(`Status Pembayaran: ${statusLabel}`, 14, 58);

      // Items table
      const tableColumn = ["Produk", "Qty", "Harga", "Total"];
      const tableRows = [] as any[][];

      if (Array.isArray(transactionDetail.items)) {
        transactionDetail.items.forEach(item => {
          const itemData = [
            item.product_name,
            item.quantity,
            formatPrice(item.price),
            formatPrice(item.price * item.quantity)
          ];
          tableRows.push(itemData);
        });
      }

      // Gunakan autoTable
      autoTable(doc, {
        head: [tableColumn],
        body: tableRows,
        startY: 65,
        theme: 'grid',
        styles: { fontSize: 9 },
        headStyles: { fillColor: [66, 135, 245] }
      });

      // Get the final Y position after the table
      const finalY = (doc as any).lastAutoTable.finalY || 120;

      // Payment summary
      doc.setFontSize(10);
      doc.text("Ringkasan Pembayaran", 14, finalY + 10);
      doc.text(`Subtotal: ${formatPrice(transactionDetail.subtotal || transactionDetail.total)}`, 14, finalY + 18);

      if (transactionDetail.tax > 0) {
        doc.text(`Pajak: ${formatPrice(transactionDetail.tax)}`, 14, finalY + 24);
        doc.text(`Total: ${formatPrice(transactionDetail.total)}`, 14, finalY + 30);
      } else {
        doc.text(`Total: ${formatPrice(transactionDetail.total)}`, 14, finalY + 24);
      }

      // Footer
      doc.setFontSize(10);
      doc.text('Terima kasih atas kunjungan Anda!', 105, finalY + 36, { align: 'center' });
      doc.text('Silahkan berkunjung kembali', 105, finalY + 42, { align: 'center' });

      // Save the PDF
      doc.save(`transaksi-${transactionDetail.id}.pdf`);

      alert('PDF berhasil dibuat dan diunduh');
      setIsPrinting(false);
    } catch (error) {
      alert('Gagal membuat PDF: ' + (error instanceof Error ? error.message : 'Unknown error'));
      setIsPrinting(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="bg-white rounded-xl shadow-sm h-full flex flex-col w-full order-panel-mobile">
      {/* Order Header */}
      <div className="p-4 border-b border-neutral-200">
        <div className="flex justify-between items-center mb-2">
          <div>
            <span className="text-xs text-neutral-500">Order #{orderNumber}</span>
            <div className="flex items-center gap-2">
              <h2 className="font-semibold">{customerName}</h2>
              <button onClick={openEditPopup} className="text-primary-600 p-1 rounded-full hover:bg-primary-50">
                <Pencil size={14} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Order Items - tambahkan min-height untuk mencegah gepeng */}
      <div className="flex-1 overflow-y-auto p-4 min-h-[200px] md:min-h-0 order-items-container">
        {cartItems.length === 0 ? (
          <div className="text-center py-8 text-neutral-400">
            <p>No items in cart</p>
            <p className="text-sm">Add items to proceed</p>
          </div>
        ) : (
          <div className="space-y-3">
            {cartItems.map((item) => (
              <div key={item.menuItem?.id} className="flex justify-between items-center border-b border-neutral-100 pb-3">
                <div>
                  <h3 className="font-medium">{item.menuItem?.name}</h3>
                  <p className="text-sm text-neutral-500">{formatPrice(item.menuItem?.price || 0)}</p>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => item.menuItem?.id && handleQuantityChange(item.menuItem.id, item.quantity - 1)}
                    className="w-6 h-6 flex items-center justify-center rounded-full border border-neutral-300 text-neutral-500 hover:bg-neutral-100"
                  >
                    -
                  </button>
                  <span className="w-6 text-center">{item.quantity}</span>
                  <button
                    onClick={() => item.menuItem?.id && handleQuantityChange(item.menuItem.id, item.quantity + 1)}
                    className="w-6 h-6 flex items-center justify-center rounded-full border border-neutral-300 text-neutral-500 hover:bg-neutral-100"
                  >
                    +
                  </button>
                  <button
                    onClick={() => item.menuItem?.id && removeFromCart(item.menuItem?.id)}
                    className="ml-2 text-neutral-400 hover:text-red-500"
                  >
                    <X size={16} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Order Summary */}
      <div className="border-t border-neutral-200 p-4">
        <div className="space-y-2 mb-4">
          <div className="flex justify-between">
            <span className="text-neutral-600">Subtotal</span>
            <span>{formatPrice(subtotal)}</span>
          </div>
          {tax > 0 && (
            <div className="flex justify-between">
              <span className="text-neutral-600">Pajak ({taxPercentage}%)</span>
              <span>{formatPrice(tax)}</span>
            </div>
          )}
          <div className="flex justify-between font-semibold text-lg mt-3">
            <span>TOTAL</span>
            <span>{formatPrice(total)}</span>
          </div>
        </div>

        <button
          className="w-full bg-primary-600 text-white py-3 rounded-md font-medium hover:bg-primary-700 transition-colors disabled:bg-neutral-300 disabled:cursor-not-allowed"
          onClick={handleCheckout}
          disabled={cartItems.length === 0}
        >
          Place Order
        </button>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-4xl mx-4 animate-fade-in flex flex-col md:flex-row">
            {/* Left side - Order details */}
            <div className="p-6 border-b md:border-b-0 md:border-r border-neutral-200 md:w-1/2">
              <h3 className="text-xl font-medium text-neutral-900 mb-4">Detail Pesanan</h3>

              <div className="mb-4">
                <div className="text-sm text-neutral-500">Order #{orderNumber}</div>
                <div className="font-medium">{customerName}</div>
              </div>

              <div className="max-h-60 overflow-y-auto mb-4">
                {cartItems.map((item) => (
                  <div key={item.menuItem?.id} className="flex justify-between items-center py-2 border-b border-neutral-100">
                    <div>
                      <div className="font-medium">{item.menuItem?.name}</div>
                      <div className="text-sm text-neutral-500">{item.quantity} x {formatPrice(item.menuItem?.price || 0)}</div>
                    </div>
                    <div className="font-medium">
                      {formatPrice((item.menuItem?.price || 0) * item.quantity)}
                    </div>
                  </div>
                ))}
              </div>

              <div className="border-t border-neutral-200 pt-4">
                <div className="flex justify-between mb-2">
                  <span className="text-neutral-600">Subtotal</span>
                  <span>{formatPrice(subtotal)}</span>
                </div>
                {tax > 0 && (
                  <div className="flex justify-between mb-2">
                    <span className="text-neutral-600">Pajak ({taxPercentage}%)</span>
                    <span>{formatPrice(tax)}</span>
                  </div>
                )}
                <div className="flex justify-between font-bold text-lg">
                  <span>TOTAL</span>
                  <span>{formatPrice(total)}</span>
                </div>
              </div>
            </div>

            {/* Right side - Payment options */}
            <div className="p-6 md:w-1/2">
              <h3 className="text-xl font-medium text-neutral-900 mb-4">Pembayaran</h3>

              {/* Payment tabs */}
              <div className="flex border-b border-neutral-200 mb-4">
                <button
                  className={`py-2 px-4 font-medium ${activeTab === 'cash'
                    ? 'text-primary-600 border-b-2 border-primary-600'
                    : 'text-neutral-500 hover:text-neutral-700'}`}
                  onClick={() => setActiveTab('cash')}
                >
                  <div className="flex items-center gap-2">
                    <Banknote size={18} />
                    <span>Tunai</span>
                  </div>
                </button>
                <button
                  className={`py-2 px-4 font-medium ${activeTab === 'non-cash'
                    ? 'text-primary-600 border-b-2 border-primary-600'
                    : 'text-neutral-500 hover:text-neutral-700'}`}
                  onClick={() => setActiveTab('non-cash')}
                >
                  <div className="flex items-center gap-2">
                    <CreditCard size={18} />
                    <span>Non Tunai</span>
                  </div>
                </button>
              </div>

              {/* Cash payment options */}
              {activeTab === 'cash' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-3">
                    <button
                      onClick={() => handlePaymentButtonClick(total)}
                      className="p-3 border border-neutral-300 rounded-md text-center hover:bg-neutral-50 transition-colors"
                    >
                      Uang Pas
                    </button>
                    <button
                      onClick={() => handlePaymentButtonClick(50000)}
                      className="p-3 border border-neutral-300 rounded-md text-center hover:bg-neutral-50 transition-colors"
                    >
                      Rp 50.000
                    </button>
                    <button
                      onClick={() => handlePaymentButtonClick(100000)}
                      className="p-3 border border-neutral-300 rounded-md text-center hover:bg-neutral-50 transition-colors"
                    >
                      Rp 100.000
                    </button>
                    <button
                      onClick={() => {
                        setCustomAmount(true);
                        setCustomAmountValue('');
                        setPaymentAmount('');
                      }}
                      className="p-3 border border-neutral-300 rounded-md text-center hover:bg-neutral-50 transition-colors"
                    >
                      Lainnya
                    </button>
                  </div>

                  {customAmount && (
                    <div className="mt-3">
                      <label htmlFor="customAmount" className="block text-sm font-medium text-neutral-700 mb-1">
                        Nominal Pembayaran
                      </label>
                      <input
                        type="text"
                        id="customAmount"
                        value={customAmountValue}
                        onChange={handleCustomAmountChange}
                        placeholder="Masukkan jumlah pembayaran"
                        className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        autoComplete="off"
                        autoFocus
                      />
                    </div>
                  )}

                  {paymentAmount && parseInt(paymentAmount) > 0 && (
                    <div className={`p-3 rounded-md ${change >= 0 ? 'bg-green-50' : 'bg-red-50'} mt-3`}>
                      <div className="flex justify-between items-center">
                        <span className="font-medium">Kembalian:</span>
                        <span className={`font-bold ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {formatPrice(change)}
                        </span>
                      </div>
                      {parseInt(paymentAmount) < total && (
                        <p className="text-sm text-red-600 mt-1">Pembayaran kurang {formatPrice(total - parseInt(paymentAmount))}</p>
                      )}
                    </div>
                  )}
                </div>
              )}

              {/* Non-cash payment options */}
              {activeTab === 'non-cash' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-3">
                    {paymentMethods.filter(method => method.type !== 'cash').length === 0 ? (
                      <div className="flex items-center justify-center h-40 bg-neutral-50 rounded-lg border border-dashed border-neutral-300">
                        <div className="text-center text-neutral-500">
                          <p className="font-medium">Tidak ada metode pembayaran non-tunai</p>
                          <p className="text-sm mt-1">Silakan gunakan pembayaran tunai</p>
                        </div>
                      </div>
                    ) : (
                      <>
                        <div className="mb-3">
                          <label className="block text-sm font-medium text-neutral-700 mb-2">
                            Pilih Metode Pembayaran
                          </label>
                          <div className="space-y-2">
                            {paymentMethods
                              .filter(method => method.type !== 'cash')
                              .map(method => (
                                <div
                                  key={method.id}
                                  className={`p-3 border rounded-md cursor-pointer transition-colors ${selectedPaymentMethod?.id === method.id
                                    ? 'border-primary-500 bg-primary-50'
                                    : 'border-neutral-300 hover:bg-neutral-50'
                                    }`}
                                  onClick={() => setSelectedPaymentMethod(method)}
                                >
                                  <div className="flex items-center justify-between">
                                    <div>
                                      <p className="font-medium">{method.name}</p>
                                      <p className="text-sm text-neutral-500">
                                        {method.type === 'bank_transfer' && 'Transfer Bank'}
                                        {method.type === 'qris' && 'QRIS'}
                                        {method.type === 'e_wallet' && 'E-Money / E-Wallet'}
                                        {method.type === 'card' && 'Kartu Kredit/Debit'}
                                        {method.type === 'other' && 'Lainnya'}
                                      </p>
                                    </div>
                                    <div className="w-5 h-5 rounded-full border border-neutral-300 flex items-center justify-center">
                                      {selectedPaymentMethod?.id === method.id && (
                                        <div className="w-3 h-3 rounded-full bg-primary-500"></div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ))}
                          </div>
                        </div>

                        {selectedPaymentMethod && (
                          <div className="mt-4 p-4 bg-neutral-50 rounded-lg border border-neutral-200">
                            <h4 className="font-medium mb-2">Detail Pembayaran</h4>

                            {selectedPaymentMethod.type === 'bank_transfer' && (
                              <div className="space-y-1 text-sm">
                                <p><span className="font-medium">Bank:</span> {selectedPaymentMethod.bank_name}</p>
                                <p><span className="font-medium">No. Rekening:</span> {selectedPaymentMethod.account_number}</p>
                                <p><span className="font-medium">Atas Nama:</span> {selectedPaymentMethod.account_name}</p>
                              </div>
                            )}

                            {selectedPaymentMethod.type === 'qris' && selectedPaymentMethod.qris_image_url && (
                              <div className="flex flex-col items-center">
                                <p className="text-sm mb-2">Scan QRIS untuk melakukan pembayaran</p>
                                <div className="border border-neutral-200 rounded-md p-2 bg-white">
                                  <img
                                    src={selectedPaymentMethod.qris_image_url}
                                    alt="QRIS"
                                    className="max-w-full h-auto max-h-48"
                                  />
                                </div>
                              </div>
                            )}

                            {selectedPaymentMethod.type === 'e_wallet' && (
                              <div className="space-y-1 text-sm">
                                <p><span className="font-medium">Provider:</span> {selectedPaymentMethod.wallet_provider}</p>
                                <p><span className="font-medium">Nomor:</span> {selectedPaymentMethod.wallet_number}</p>
                              </div>
                            )}

                            <div className="mt-3 pt-3 border-t border-neutral-200">
                              <p className="text-sm text-neutral-600">
                                Total yang harus dibayar: <span className="font-bold">{formatPrice(total)}</span>
                              </p>
                            </div>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              )}

              <div className="flex justify-end gap-3 mt-6">
                <button
                  onClick={() => setShowPaymentModal(false)}
                  className="px-4 py-2 border border-neutral-300 rounded-md text-neutral-700 hover:bg-neutral-50"
                >
                  Batal
                </button>
                <button
                  onClick={handleConfirmPayment}
                  disabled={
                    (activeTab === 'cash' && (!paymentAmount || parseFloat(paymentAmount) < total)) ||
                    (activeTab === 'non-cash' && !selectedPaymentMethod)
                  }
                  className={`px-4 py-2 rounded-md text-white font-medium ${(activeTab === 'cash' && paymentAmount && parseFloat(paymentAmount) >= total) ||
                    (activeTab === 'non-cash' && selectedPaymentMethod)
                    ? 'bg-primary-600 hover:bg-primary-700'
                    : 'bg-neutral-400 cursor-not-allowed'
                    }`}
                >
                  Bayar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 animate-fade-in">
            <div className="flex flex-col items-center mb-6">
              <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
                <CheckCircle size={32} className="text-green-500" />
              </div>
              <h3 className="text-xl font-medium text-neutral-900 mb-1">Transaksi Berhasil</h3>
              <p className="text-neutral-600 text-center">Pembayaran telah diterima dan transaksi telah disimpan.</p>
            </div>

            {/* Informasi pembayaran */}
            <div className="bg-neutral-50 p-4 rounded-lg mb-6">
              <div className="flex justify-between mb-2">
                <span className="text-neutral-600">Total</span>
                <span className="font-medium">{formatPrice(successData.total)}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-neutral-600">Pembayaran</span>
                <span className="font-medium">{formatPrice(successData.payment)}</span>
              </div>
              {!successData.isNonCash && (
                <div className={`flex justify-between items-center p-3 rounded-md transition-all duration-300 ${successData.change > 0
                  ? 'bg-gradient-to-r from-green-50 to-green-100 border border-green-200 shadow-sm'
                  : 'bg-neutral-50'
                  }`}>
                  <div className="flex items-center gap-2">
                    {successData.change > 0 && (
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">💰</span>
                      </div>
                    )}
                    <span className={`${successData.change > 0 ? 'text-green-700 font-semibold' : 'text-neutral-600'}`}>
                      Kembalian
                    </span>
                  </div>
                  <span className={`font-bold ${successData.change > 0 ? 'text-green-700 text-xl' : 'font-medium text-neutral-800'}`}>
                    {formatPrice(successData.change)}
                  </span>
                </div>
              )}
              <div className="flex justify-between mt-2 pt-2 border-t border-neutral-200">
                <span className="text-neutral-600">Metode</span>
                <span className="font-medium">{successData.paymentMethod}</span>
              </div>
            </div>

            {/* QR Code untuk Payment Gateway */}
            {successData.isGateway && successData.gatewayData?.qr_code_url && (
              <div className="bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 p-4 rounded-lg mb-6 shadow-sm">
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">📱</span>
                  </div>
                  <h4 className="font-semibold text-blue-700">Scan QR Code untuk Pembayaran</h4>
                </div>

                <div className="flex justify-center mb-3">
                  <div className="bg-white p-3 rounded-lg border border-blue-200 shadow-sm">
                    <img
                      src={successData.gatewayData.qr_code_url}
                      alt="QR Code Pembayaran"
                      className="w-48 h-48 object-contain"
                      onError={(e) => {
                        console.error('Error loading QR code');
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  </div>
                </div>

                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-1">
                    {isPolling && (
                      <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    )}
                    <p className={`text-sm font-medium ${successData.gatewayData?.transaction_status === 'settlement' ||
                      successData.gatewayData?.transaction_status === 'capture'
                      ? 'text-green-600'
                      : successData.gatewayData?.transaction_status === 'failure' ||
                        successData.gatewayData?.transaction_status === 'cancel' ||
                        successData.gatewayData?.transaction_status === 'expire'
                        ? 'text-red-600'
                        : 'text-blue-600'
                      }`}>
                      Status: {
                        successData.gatewayData?.transaction_status === 'settlement' ||
                          successData.gatewayData?.transaction_status === 'capture'
                          ? 'Pembayaran Berhasil ✅'
                          : successData.gatewayData?.transaction_status === 'failure' ||
                            successData.gatewayData?.transaction_status === 'cancel' ||
                            successData.gatewayData?.transaction_status === 'expire'
                            ? 'Pembayaran Dibatalkan ❌'
                            : 'Menunggu Pembayaran ⏳'
                      }
                    </p>
                  </div>
                  {successData.gatewayData?.transaction_status === 'pending' && (
                    <p className="text-xs text-blue-500">
                      QR Code akan kedaluwarsa dalam 10 menit
                    </p>
                  )}
                  {isPolling && (
                    <p className="text-xs text-blue-400 mt-1">
                      Mengecek status pembayaran...
                    </p>
                  )}
                </div>
              </div>
            )}

            <div className="space-y-3">
              <button
                onClick={closeSuccessModal}
                className="w-full py-3 bg-primary-600 text-white rounded-md font-medium hover:bg-primary-700 transition-colors"
              >
                Selesai
              </button>

              {/* Tombol Detail Transaksi */}
              <button
                onClick={handleViewTransactionDetail}
                className="w-full py-3 border border-primary-600 text-primary-600 rounded-md font-medium hover:bg-primary-50 transition-colors"
              >
                Lihat Detail Transaksi
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Customer Name Popup */}
      {showEditPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-lg font-semibold mb-4">Edit Customer Information</h2>

            <div className="mb-4">
              <label htmlFor="customerName" className="block text-sm font-medium text-neutral-700 mb-1">
                Customer Name
              </label>
              <input
                type="text"
                id="customerName"
                value={tempCustomerName}
                onChange={(e) => setTempCustomerName(e.target.value)}
                className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                autoComplete="off"
              />
            </div>

            <div className="flex flex-col gap-3 mb-4">
              <button
                onClick={openMemberModal}
                className="w-full px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 flex items-center justify-center"
              >
                <Users size={18} className="mr-2" />
                Pilih dari Member
              </button>
            </div>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setShowEditPopup(false)}
                className="px-4 py-2 border border-neutral-300 rounded-md text-neutral-700 hover:bg-neutral-50"
              >
                Cancel
              </button>
              <button
                onClick={saveCustomerName}
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Member Selection Modal */}
      {showMemberModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] flex flex-col">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Pilih Member</h2>
              <button onClick={closeMemberModal} className="text-neutral-500 hover:text-neutral-700">
                <X size={20} />
              </button>
            </div>

            {!showAddMemberForm ? (
              <>
                <div className="mb-4 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search size={18} className="text-neutral-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Cari member..."
                    className="w-full pl-10 pr-4 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    autoComplete="off"
                  />
                </div>

                <div className="flex justify-end mb-4">
                  <button
                    onClick={() => setShowAddMemberForm(true)}
                    className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 flex items-center"
                  >
                    <Plus size={18} className="mr-2" />
                    Tambah Member Baru
                  </button>
                </div>

                <div className="overflow-y-auto flex-1 border border-neutral-200 rounded-lg">
                  {isLoadingMembers ? (
                    <div className="text-center py-8">
                      <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary-500 border-t-transparent"></div>
                      <p className="mt-2 text-neutral-600">Memuat data member...</p>
                    </div>
                  ) : filteredMembers.length === 0 ? (
                    <div className="text-center py-8 text-neutral-500">
                      <p>Tidak ada member yang ditemukan</p>
                    </div>
                  ) : (
                    <table className="w-full">
                      <thead className="bg-neutral-50">
                        <tr className="border-b border-neutral-200">
                          <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Nama</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Telepon</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Email</th>
                          <th className="px-4 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider">Aksi</th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredMembers.map((member) => (
                          <tr key={member.id} className="border-b border-neutral-100 hover:bg-neutral-50">
                            <td className="px-4 py-3 font-medium text-neutral-800">{member.name}</td>
                            <td className="px-4 py-3 text-neutral-600">{member.phone}</td>
                            <td className="px-4 py-3 text-neutral-600">{member.email || '-'}</td>
                            <td className="px-4 py-3">
                              <div className="flex justify-end">
                                <button
                                  onClick={() => selectMember(member)}
                                  className="px-3 py-1 bg-primary-600 text-white rounded-md hover:bg-primary-700 text-sm"
                                >
                                  Pilih
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  )}
                </div>
              </>
            ) : (
              <form onSubmit={handleSaveMember} className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-neutral-700 mb-1">
                    Nama <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={newMemberData.name}
                    onChange={handleNewMemberChange}
                    className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Masukkan nama member"
                    required
                    autoComplete="off"
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-neutral-700 mb-1">
                    Nomor Telepon <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="phone"
                    name="phone"
                    value={newMemberData.phone}
                    onChange={handleNewMemberChange}
                    className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Masukkan nomor telepon"
                    required
                    autoComplete="off"
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-neutral-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={newMemberData.email}
                    onChange={handleNewMemberChange}
                    className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Masukkan email (opsional)"
                    autoComplete="off"
                  />
                </div>

                <div className="flex justify-end gap-3 pt-2">
                  <button
                    type="button"
                    onClick={() => setShowAddMemberForm(false)}
                    className="px-4 py-2 border border-neutral-300 text-neutral-700 rounded-md hover:bg-neutral-50"
                  >
                    Batal
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 flex items-center"
                    disabled={isSavingMember}
                  >
                    {isSavingMember ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Menyimpan...
                      </>
                    ) : (
                      <>
                        <Plus className="h-4 w-4 mr-2" />
                        Simpan Member
                      </>
                    )}
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}

      {/* Status Update Modal */}
      {showStatusUpdateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 animate-fade-in">
            <div className="text-center mb-6">
              <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full ${statusUpdateType === 'success' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                } mb-4`}>
                {statusUpdateType === 'success' ? (
                  <CheckCircle size={32} />
                ) : (
                  <X size={32} />
                )}
              </div>
              <h3 className="text-xl font-medium text-neutral-900">
                {statusUpdateType === 'success' ? 'Berhasil' : 'Gagal'}
              </h3>
              <p className="text-neutral-600 mt-2">{statusUpdateMessage}</p>
            </div>
          </div>
        </div>
      )}

      {/* Transaction Detail Modal */}
      {showTransactionDetailModal && transactionDetail && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto flex flex-col">
            {/* Header dengan tombol aksi */}
            <div className="sticky top-0 bg-white p-4 border-b border-neutral-200 flex justify-between items-center z-10">
              <h3 className="text-lg font-medium text-neutral-900">Detail Transaksi</h3>
              <div className="flex gap-2">
                <button
                  onClick={handlePrintOptions}
                  className="p-2 text-primary-600 hover:bg-primary-50 rounded-full flex items-center gap-1"
                  disabled={isPrinting}
                >
                  {isPrinting ? (
                    <>
                      <span className="animate-spin mr-1">⏳</span>
                      <span className="hidden xs:inline">Mencetak...</span>
                    </>
                  ) : (
                    <>
                      <Printer size={20} />
                      <span className="hidden sm:inline">Cetak</span>
                    </>
                  )}
                </button>
                <button
                  onClick={() => setShowTransactionDetailModal(false)}
                  className="p-2 text-neutral-600 hover:bg-neutral-100 rounded-full"
                >
                  <X size={20} />
                </button>
              </div>
            </div>

            {/* Konten receipt yang dapat di-scroll */}
            <div className="p-4 overflow-y-auto flex-1">
              <div ref={printRef} className="print-content">
                <div className="text-center mb-6">
                  <h2 className="text-xl font-bold">{storeConfig.store_name}</h2>
                  <p className="text-neutral-600">{transactionDetail.branch_name || 'Cabang Utama'}</p>
                  <p className="text-neutral-600 text-sm">
                    {formatDate(transactionDetail.date)}
                  </p>
                </div>

                <div className="mb-4">
                  <p><span className="font-medium">ID Transaksi:</span> {transactionDetail.id}</p>
                  <p><span className="font-medium">Pelanggan:</span> {transactionDetail.customer_name}</p>
                </div>

                {/* Tabel item dengan overflow horizontal pada mobile */}
                <div className="overflow-x-auto mb-6">
                  <table className="w-full min-w-[400px]">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2">Item</th>
                        <th className="text-center py-2">Qty</th>
                        <th className="text-right py-2">Harga</th>
                        <th className="text-right py-2">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {/* Tambahkan pengecekan untuk memastikan items ada dan merupakan array */}
                      {Array.isArray(transactionDetail.items) && transactionDetail.items.length > 0 ? (
                        transactionDetail.items.map((item, index) => (
                          <tr key={index} className="border-b border-dotted">
                            <td className="py-2">{item.product_name}</td>
                            <td className="text-center py-2">{item.quantity}x</td>
                            <td className="text-right py-2">{formatPrice(item.price)}</td>
                            <td className="text-right py-2">{formatPrice(item.price * item.quantity)}</td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={4} className="py-4 text-center text-neutral-500">
                            Tidak ada item dalam transaksi ini
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>

                {/* Informasi total dengan layout yang responsif */}
                <div className="border-t pt-2 space-y-1">
                  <div className="flex justify-between py-1">
                    <span>Subtotal</span>
                    <span>{formatPrice(transactionDetail.subtotal)}</span>
                  </div>
                  <div className="flex justify-between py-1">
                    <span>Pajak</span>
                    <span>{formatPrice(transactionDetail.tax)}</span>
                  </div>
                  <div className="flex justify-between py-1 font-bold">
                    <span>Total</span>
                    <span>{formatPrice(transactionDetail.total)}</span>
                  </div>
                  <div className="flex justify-between py-1">
                    <span>Dibayar</span>
                    <span>{formatPrice(transactionDetail.amount_paid)}</span>
                  </div>
                  <div className="flex justify-between py-1">
                    <span>Kembalian</span>
                    <span>{formatPrice(transactionDetail.change_amount)}</span>
                  </div>
                </div>

                {/* Informasi metode pembayaran */}
                <div className="mt-4 pt-3 border-t border-neutral-200">
                  <h4 className="font-medium mb-2">Metode Pembayaran</h4>
                  <p className="mb-2">{getPaymentMethodLabel(transactionDetail)}</p>

                  {/* Tampilkan detail metode pembayaran */}
                  {getPaymentMethodInfo(transactionDetail)}
                </div>

                <div className="text-center mt-6 text-neutral-500 text-sm">
                  <p>Terima kasih atas kunjungan Anda</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Print Options Modal */}
      {showPrintOptions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[70] p-4">
          <div className="bg-white rounded-lg w-full max-w-md p-6 animate-fade-in">
            <h3 className="text-lg font-medium text-neutral-900 mb-4">Pilih Metode Cetak</h3>

            <div className="space-y-3">
              <button
                onClick={printToBluetoothPrinter}
                className="w-full flex items-center justify-between p-4 border border-neutral-200 rounded-lg hover:bg-primary-50 hover:border-primary-200 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="bg-primary-100 p-2 rounded-full">
                    <Bluetooth size={20} className="text-primary-600" />
                  </div>
                  <div className="text-left">
                    <p className="font-medium text-neutral-800">Cetak Struk</p>
                    <p className="text-sm text-neutral-500">Cetak ke printer thermal Bluetooth</p>
                  </div>
                </div>
              </button>

              <button
                onClick={printToPDF}
                className="w-full flex items-center justify-between p-4 border border-neutral-200 rounded-lg hover:bg-primary-50 hover:border-primary-200 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="bg-primary-100 p-2 rounded-full">
                    <FileText size={20} className="text-primary-600" />
                  </div>
                  <div className="text-left">
                    <p className="font-medium text-neutral-800">Cetak PDF</p>
                    <p className="text-sm text-neutral-500">Unduh sebagai file PDF</p>
                  </div>
                </div>
              </button>
            </div>

            <div className="mt-6 flex justify-end">
              <button
                onClick={() => setShowPrintOptions(false)}
                className="px-4 py-2 text-neutral-600 hover:bg-neutral-100 rounded-md"
              >
                Batal
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderPanel;


















