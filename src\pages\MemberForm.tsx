import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { ArrowLeft, Save } from 'lucide-react';
import config from '../config';
import { fetchWithSession, getCurrentUser } from '../utils/api';

interface MemberFormData {
  name: string;
  phone: string;
  email: string;
  address: string;
  birthdate: string;
  is_active: boolean;
  notes: string;
}

const MemberForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = id !== undefined && id !== 'new';

  // Inisialisasi state formData
  const [formData, setFormData] = useState<MemberFormData>({
    name: '',
    phone: '',
    email: '',
    address: '',
    birthdate: '',
    is_active: true,
    notes: ''
  });

  const [errors, setErrors] = useState<Partial<Record<keyof MemberFormData, string>>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    // Hanya fetch data jika dalam mode edit
    if (isEditMode) {
      fetchMemberData();
    }
  }, [id, isEditMode]);

  const fetchMemberData = async () => {
    setIsLoading(true);
    try {
      // Dapatkan user_id dari user yang sedang login
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;

      const response = await fetchWithSession(`${config.apiUrl}/members/${id}?user_id=${userId}`);

      if (response && response.ok) {
        const data = await response.json();
        setFormData({
          name: data.name,
          phone: data.phone,
          email: data.email || '',
          address: data.address || '',
          birthdate: data.birthdate || '',
          is_active: data.is_active === 1,
          notes: data.notes || ''
        });
      } else {
        navigate('/members');
        alert('Member tidak ditemukan');
      }
    } catch (error) {
      navigate('/members');
      alert('Terjadi kesalahan saat mengambil data member');
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear error when field is edited
    if (errors[name as keyof MemberFormData]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const validateForm = () => {
    const newErrors: Partial<Record<keyof MemberFormData, string>> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nama harus diisi';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Nomor telepon harus diisi';
    } else if (!/^[0-9+\-\s]+$/.test(formData.phone)) {
      newErrors.phone = 'Nomor telepon tidak valid';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Format email tidak valid';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validasi form
    if (!validateForm()) {
      return;
    }

    setIsSaving(true);

    try {
      // Dapatkan user_id dari user yang sedang login
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;

      const url = isEditMode
        ? `${config.apiUrl}/members/${id}`
        : `${config.apiUrl}/members`;

      const method = isEditMode ? 'PUT' : 'POST';

      const response = await fetchWithSession(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: formData.name,
          phone: formData.phone,
          email: formData.email || null,
          address: formData.address || null,
          birthdate: formData.birthdate || null,
          is_active: formData.is_active ? 1 : 0,
          notes: formData.notes || null,
          user_id: userId
        })
      });

      if (response && response.ok) {
        navigate('/members');
      } else {
        const errorData = await response?.json();
        if (errorData?.error) {
          alert(errorData.error);
        } else {
          alert('Terjadi kesalahan saat menyimpan data');
        }
      }
    } catch (error) {
      alert('Terjadi kesalahan saat menyimpan data');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center mb-6">
          <Link to="/members" className="p-2 rounded-full hover:bg-neutral-100">
            <ArrowLeft size={20} />
          </Link>
          <h1 className="text-2xl font-bold text-primary-800 ml-2">
            {isEditMode ? 'Edit Member' : 'Tambah Member Baru'}
          </h1>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <Link to="/members" className="p-2 rounded-full hover:bg-neutral-100">
          <ArrowLeft size={20} />
        </Link>
        <h1 className="text-2xl font-bold text-primary-800 ml-2">
          {isEditMode ? 'Edit Member' : 'Tambah Member Baru'}
        </h1>
      </div>

      <div className="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6" autoComplete="off">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-neutral-700 mb-1">
                Nama <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className={`w-full px-3 py-2 border ${errors.name ? 'border-red-500' : 'border-neutral-300'} rounded-lg focus:ring-primary-500 focus:border-primary-500`}
                placeholder="Masukkan nama member"
                autoComplete="off"
              />
              {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
            </div>

            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-neutral-700 mb-1">
                Nomor Telepon <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                className={`w-full px-3 py-2 border ${errors.phone ? 'border-red-500' : 'border-neutral-300'} rounded-lg focus:ring-primary-500 focus:border-primary-500`}
                placeholder="Masukkan nomor telepon"
                autoComplete="off"
              />
              {errors.phone && <p className="mt-1 text-sm text-red-500">{errors.phone}</p>}
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-neutral-700 mb-1">
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full px-3 py-2 border ${errors.email ? 'border-red-500' : 'border-neutral-300'} rounded-lg focus:ring-primary-500 focus:border-primary-500`}
                placeholder="Masukkan email (opsional)"
                autoComplete="off"
              />
              {errors.email && <p className="mt-1 text-sm text-red-500">{errors.email}</p>}
            </div>

            <div>
              <label htmlFor="birthdate" className="block text-sm font-medium text-neutral-700 mb-1">
                Tanggal Lahir
              </label>
              <input
                type="date"
                id="birthdate"
                name="birthdate"
                value={formData.birthdate}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                autoComplete="off"
              />
            </div>

            <div>
              <label htmlFor="address" className="block text-sm font-medium text-neutral-700 mb-1">
                Alamat
              </label>
              <textarea
                id="address"
                name="address"
                value={formData.address}
                onChange={handleChange}
                rows={3}
                className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                placeholder="Masukkan alamat (opsional)"
                autoComplete="off"
              ></textarea>
            </div>

            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-neutral-700 mb-1">
                Catatan
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows={3}
                className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                placeholder="Masukkan catatan (opsional)"
                autoComplete="off"
              ></textarea>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_active"
                name="is_active"
                checked={formData.is_active}
                onChange={handleChange}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded"
              />
              <label htmlFor="is_active" className="ml-2 block text-sm text-neutral-700">
                Member Aktif
              </label>
            </div>
          </div>

          <div className="mt-8 flex justify-end">
            <Link
              to="/members"
              className="px-4 py-2 border border-neutral-300 text-neutral-700 rounded-lg mr-2 hover:bg-neutral-50"
            >
              Batal
            </Link>
            <button
              type="submit"
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 flex items-center"
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Menyimpan...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Simpan
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MemberForm;




