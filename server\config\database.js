const mysql = require('mysql2/promise');
require('dotenv').config();

const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'satulisa_db',
  password: process.env.DB_PASSWORD || 'owr216he890',
  database: process.env.DB_NAME || 'satulisa_db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  timezone: '+07:00', // Set timezone ke Asia/Jakarta (GMT+7)
  dateStrings: [
    'DATE',
    'DATETIME'
  ] // Ini akan memastikan tanggal dikembalikan sebagai string, bukan objek Date
});

// Test connection
pool.getConnection()
  .then(connection => {
    console.log('Database connected successfully');
    connection.release();
  })
  .catch(err => {
    console.error('Database connection error:', err);
  });

module.exports = pool;

