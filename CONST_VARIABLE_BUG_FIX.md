# Const Variable Assignment Bug Fix

## Error Message
```
"Failed to fetch products : Assignment to constant variable."
```

## Problem

Error terjadi karena variabel `user_id` dideklar<PERSON><PERSON> sebagai `const` tapi kemudian dicoba untuk diubah nilainya dalam logika kasir.

## Root Cause

### **server/routes/products.js:**
```javascript
// PROBLEMATIC CODE
const { user_id, cashier_id } = req.query;  // user_id declared as const
let { branch_id } = req.query;

if (cashier_id) {
  // ... logic ...
  if (cashierResult[0].admin_id) {
    user_id = cashierResult[0].admin_id;  // ❌ ERROR: Assignment to const variable
  }
}
```

### **server/routes/categories.js:**
```javascript
// SAME PROBLEM
const { user_id, cashier_id } = req.query;  // user_id declared as const
let { branch_id } = req.query;

if (cashier_id) {
  // ... logic ...
  if (cashierResult[0].branch_id) {
    user_id = cashierResult[0].branch_id;  // ❌ ERROR: Assignment to const variable
  } else if (cashierResult[0].admin_id) {
    user_id = cashierResult[0].admin_id;   // ❌ ERROR: Assignment to const variable
  }
}
```

## Solution

Change `const` to `let` for variables that need to be reassigned:

### **server/routes/products.js:**
```javascript
// FIXED CODE
let { user_id, cashier_id } = req.query;  // ✅ Changed to let
let { branch_id } = req.query;

if (cashier_id) {
  // ... logic ...
  if (cashierResult[0].admin_id) {
    user_id = cashierResult[0].admin_id;  // ✅ Now works
  }
}
```

### **server/routes/categories.js:**
```javascript
// FIXED CODE
let { user_id, cashier_id } = req.query;  // ✅ Changed to let
let { branch_id } = req.query;

if (cashier_id) {
  // ... logic ...
  if (cashierResult[0].branch_id) {
    user_id = cashierResult[0].branch_id;  // ✅ Now works
  } else if (cashierResult[0].admin_id) {
    user_id = cashierResult[0].admin_id;   // ✅ Now works
  }
}
```

## Technical Explanation

### **JavaScript const vs let:**

#### **const:**
- Creates a **constant** reference
- Cannot be reassigned after declaration
- Must be initialized at declaration
- Block-scoped

```javascript
const x = 5;
x = 10;  // ❌ TypeError: Assignment to constant variable
```

#### **let:**
- Creates a **variable** reference
- Can be reassigned after declaration
- Can be declared without initialization
- Block-scoped

```javascript
let x = 5;
x = 10;  // ✅ Works fine
```

### **Why This Happened:**

1. **Initial Code**: Used `const` for destructuring query parameters
2. **Cashier Logic**: Needed to reassign `user_id` based on cashier's admin/branch
3. **Conflict**: `const` prevents reassignment, causing runtime error

### **When the Error Occurs:**

The error only happens when:
- ✅ **Request has `cashier_id` parameter**
- ✅ **Cashier exists in database**
- ✅ **Cashier has `admin_id` or `branch_id`**
- ✅ **Code tries to reassign `user_id`**

This is why the error might not appear in all scenarios - it's specific to cashier requests.

## Files Changed

### 1. **server/routes/products.js**
- **Line 9**: Changed `const { user_id, cashier_id }` to `let { user_id, cashier_id }`
- **Reason**: Allows reassignment of `user_id` in cashier logic

### 2. **server/routes/categories.js**
- **Line 8**: Changed `const { user_id, cashier_id }` to `let { user_id, cashier_id }`
- **Reason**: Allows reassignment of `user_id` in cashier logic

## Testing

### **Test Scenarios:**

1. **Admin Request**: Should work (no reassignment needed)
2. **Branch Request**: Should work (no reassignment needed)
3. **Direct Admin Cashier**: Should work (reassignment happens)
4. **Branch Cashier**: Should work (reassignment happens)

### **Before Fix:**
- ❌ Cashier requests fail with "Assignment to constant variable"
- ✅ Admin/Branch requests work fine

### **After Fix:**
- ✅ All request types work correctly
- ✅ Cashier logic executes without errors
- ✅ Proper access control maintained

## Prevention

### **Best Practices:**

1. **Use `let` for variables that might be reassigned**
2. **Use `const` only for truly constant values**
3. **Consider the full lifecycle of variables**
4. **Test all code paths, especially conditional logic**

### **Code Review Checklist:**
- [ ] Check if destructured variables are reassigned later
- [ ] Verify `const` vs `let` usage is appropriate
- [ ] Test conditional branches that modify variables
- [ ] Ensure error handling covers all scenarios

## Impact

### **Before Fix:**
- 🚫 **Cashier Login**: Fails to load products/categories
- 🚫 **POS System**: Cannot function for cashiers
- 🚫 **Branch Operations**: Cashiers cannot work

### **After Fix:**
- ✅ **Cashier Login**: Works correctly
- ✅ **POS System**: Functions properly for all user types
- ✅ **Branch Operations**: Cashiers can access their data
- ✅ **Access Control**: Proper isolation maintained

## Related Changes

This fix is part of the cashier branch access control implementation:
- **Products**: Cashiers see only relevant products
- **Categories**: Cashiers see only relevant categories
- **Isolation**: Complete branch separation maintained

The const/let fix ensures the access control logic can execute properly without runtime errors.

## Deployment Notes

- **Server Restart**: Required after fixing the files
- **No Database Changes**: This is purely a code fix
- **Immediate Effect**: Error should disappear after restart
- **Backward Compatible**: No breaking changes to API

The fix resolves the runtime error while maintaining all the intended access control functionality for cashier users.
