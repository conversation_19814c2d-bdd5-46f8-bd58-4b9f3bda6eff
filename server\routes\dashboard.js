const express = require('express');
const router = express.Router();
const pool = require('../config/database');

// Get dashboard summary
router.get('/summary', async (req, res) => {
  try {
    const { user_id, branch_id, date_filter = 'today' } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Build date filter conditions
    let dateCondition = '';
    const now = new Date();

    switch (date_filter) {
      case 'today':
        const today = now.toISOString().split('T')[0];
        dateCondition = `AND DATE(t.date) = '${today}'`;
        break;
      case 'week':
        const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
        dateCondition = `AND t.date >= '${weekStart.toISOString().split('T')[0]}'`;
        break;
      case 'month':
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        dateCondition = `AND t.date >= '${monthStart.toISOString().split('T')[0]}'`;
        break;
      case 'year':
        const yearStart = new Date(now.getFullYear(), 0, 1);
        dateCondition = `AND t.date >= '${yearStart.toISOString().split('T')[0]}'`;
        break;
    }

    // Build branch filter condition
    let branchCondition = '';
    if (branch_id) {
      branchCondition = `AND t.branch_id = ${branch_id}`;
    }

    // Base condition for user access - menangani kasir yang dibuat admin langsung dan melalui cabang
    let userCondition = '';
    if (branch_id) {
      // Jika ada branch_id yang dipilih, filter berdasarkan branch_id
      // Untuk branch user: hanya tampilkan transaksi dari branch mereka
      // Untuk admin: tampilkan transaksi dari branch yang dipilih + transaksi langsung admin
      userCondition = `(
        t.branch_id = ${branch_id} OR
        t.cashier_id IN (SELECT id FROM cashiers WHERE branch_id = ${branch_id}) OR
        (t.outlet_id = ${user_id} AND t.branch_id IS NULL)
      )`;
    } else {
      // Jika tidak ada branch_id, tampilkan semua transaksi yang terkait dengan admin:
      // 1. Transaksi langsung dari admin (outlet_id)
      // 2. Transaksi dari cabang milik admin (branch_id)
      // 3. Transaksi dari kasir yang dibuat admin langsung (cashier_id dengan user_id = admin)
      // 4. Transaksi dari kasir yang dibuat melalui cabang (cashier_id dengan branch_id milik admin)
      userCondition = `(
        t.outlet_id = ${user_id} OR
        t.branch_id IN (SELECT id FROM branches WHERE user_id = ${user_id}) OR
        t.cashier_id IN (SELECT id FROM cashiers WHERE user_id = ${user_id}) OR
        t.cashier_id IN (SELECT id FROM cashiers WHERE branch_id IN (SELECT id FROM branches WHERE user_id = ${user_id}))
      )`;
    }

    // Get total sales and transactions
    const [salesData] = await pool.query(`
      SELECT
        COALESCE(SUM(t.total), 0) as total_sales,
        COUNT(t.id) as total_transactions
      FROM transactions t
      WHERE ${userCondition} ${dateCondition} AND t.payment_status = 'paid'
    `);

    // Get today's sales and transactions
    const todayDate = new Date().toISOString().split('T')[0];
    const [todayData] = await pool.query(`
      SELECT
        COALESCE(SUM(t.total), 0) as today_sales,
        COUNT(t.id) as today_transactions
      FROM transactions t
      WHERE ${userCondition} AND DATE(t.date) = '${todayDate}' AND t.payment_status = 'paid'
    `);

    // Get month's sales and transactions
    const monthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
    const [monthData] = await pool.query(`
      SELECT
        COALESCE(SUM(t.total), 0) as month_sales,
        COUNT(t.id) as month_transactions
      FROM transactions t
      WHERE ${userCondition} AND t.date >= '${monthStart.toISOString().split('T')[0]}' AND t.payment_status = 'paid'
    `);

    // Get total customers (unique customer names)
    const [customerData] = await pool.query(`
      SELECT COUNT(DISTINCT t.customer_name) as total_customers
      FROM transactions t
      WHERE ${userCondition} AND t.payment_status = 'paid'
    `);

    // Get total products
    let productQuery = `
      SELECT COUNT(*) as total_products
      FROM products p
      WHERE p.is_active = 1
    `;

    if (branch_id) {
      // For branch users or admin filtering by branch: show products from that branch or admin's products
      productQuery += ` AND (p.user_id IN (SELECT user_id FROM branches WHERE id = ${branch_id}) OR p.user_id = ${user_id})`;
    } else {
      // For admin without branch filter: show all their products and their branches' products
      productQuery += ` AND (p.user_id = ${user_id} OR p.user_id IN (SELECT user_id FROM branches WHERE user_id = ${user_id}))`;
    }

    const [productData] = await pool.query(productQuery);

    // Get top products
    let topProductsQuery = `
      SELECT
        p.id,
        p.name,
        SUM(ti.quantity) as quantity,
        SUM(ti.quantity * ti.price) as sales
      FROM transaction_items ti
      JOIN transactions t ON ti.transaction_id = t.id
      JOIN products p ON ti.product_id = p.id
      WHERE ${userCondition} ${dateCondition} AND t.payment_status = 'paid'
      GROUP BY p.id, p.name
      ORDER BY sales DESC
      LIMIT 10
    `;

    const [topProducts] = await pool.query(topProductsQuery);

    // Get recent transactions
    let recentTransactionsQuery = `
      SELECT
        t.id,
        t.customer_name,
        t.total,
        t.created_at as date,
        t.payment_method,
        t.payment_status
      FROM transactions t
      WHERE ${userCondition}
      ORDER BY t.created_at DESC
      LIMIT 10
    `;

    const [recentTransactions] = await pool.query(recentTransactionsQuery);

    // Prepare response data
    const summary = {
      totalSales: salesData[0].total_sales || 0,
      totalTransactions: salesData[0].total_transactions || 0,
      totalCustomers: customerData[0].total_customers || 0,
      totalProducts: productData[0].total_products || 0,
      todaySales: todayData[0].today_sales || 0,
      todayTransactions: todayData[0].today_transactions || 0,
      monthSales: monthData[0].month_sales || 0,
      monthTransactions: monthData[0].month_transactions || 0,
      topProducts: topProducts || [],
      recentTransactions: recentTransactions || []
    };

    res.json(summary);
  } catch (error) {
    console.error('Error fetching dashboard summary:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard summary' });
  }
});

// Get sales chart data
router.get('/sales-chart', async (req, res) => {
  try {
    const { user_id, branch_id, period = 'week' } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    let dateFormat = '';
    let dateCondition = '';
    const now = new Date();

    switch (period) {
      case 'week':
        dateFormat = 'DATE(created_at)';
        const weekStart = new Date(now.setDate(now.getDate() - 7));
        dateCondition = `AND created_at >= '${weekStart.toISOString().split('T')[0]}'`;
        break;
      case 'month':
        dateFormat = 'DATE(created_at)';
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        dateCondition = `AND created_at >= '${monthStart.toISOString().split('T')[0]}'`;
        break;
      case 'year':
        dateFormat = 'DATE_FORMAT(created_at, "%Y-%m")';
        const yearStart = new Date(now.getFullYear(), 0, 1);
        dateCondition = `AND created_at >= '${yearStart.toISOString().split('T')[0]}'`;
        break;
    }

    // Build user condition for chart data - sama seperti summary
    let userCondition = '';
    if (branch_id) {
      // Jika ada branch_id yang dipilih, filter berdasarkan branch_id
      // Untuk branch user: hanya tampilkan transaksi dari branch mereka
      // Untuk admin: tampilkan transaksi dari branch yang dipilih + transaksi langsung admin
      userCondition = `(
        branch_id = ${branch_id} OR
        cashier_id IN (SELECT id FROM cashiers WHERE branch_id = ${branch_id}) OR
        (outlet_id = ${user_id} AND branch_id IS NULL)
      )`;
    } else {
      // Jika tidak ada branch_id, tampilkan semua transaksi yang terkait dengan admin:
      // 1. Transaksi langsung dari admin (outlet_id)
      // 2. Transaksi dari cabang milik admin (branch_id)
      // 3. Transaksi dari kasir yang dibuat admin langsung (cashier_id dengan user_id = admin)
      // 4. Transaksi dari kasir yang dibuat melalui cabang (cashier_id dengan branch_id milik admin)
      userCondition = `(
        outlet_id = ${user_id} OR
        branch_id IN (SELECT id FROM branches WHERE user_id = ${user_id}) OR
        cashier_id IN (SELECT id FROM cashiers WHERE user_id = ${user_id}) OR
        cashier_id IN (SELECT id FROM cashiers WHERE branch_id IN (SELECT id FROM branches WHERE user_id = ${user_id}))
      )`;
    }

    const query = `
      SELECT
        ${dateFormat} as date,
        SUM(total) as sales,
        COUNT(*) as transactions
      FROM transactions
      WHERE ${userCondition} ${dateCondition} AND payment_status = 'paid'
      GROUP BY ${dateFormat}
      ORDER BY date ASC
    `;

    const [chartData] = await pool.query(query);

    res.json(chartData);
  } catch (error) {
    console.error('Error fetching sales chart data:', error);
    res.status(500).json({ error: 'Failed to fetch sales chart data' });
  }
});

module.exports = router;
